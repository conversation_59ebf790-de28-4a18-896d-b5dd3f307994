import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import SmartDashboard from '../../src/components/improved/SmartDashboard';

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}));

const mockUserProgress = {
  hasBasicInfo: true,
  hasIncomeData: true,
  hasExpenseData: true,
  hasSavingsGoals: true,
  hasSwissConfig: true,
  completionPercentage: 85,
};

const mockFinancialData = {
  totalMonthlyIncome: 8000,
  totalExpenses: 5000,
  savingsRate: 37.5,
  fireProgress: 25,
  monthsToFire: 180,
  currentBalance: 150000,
  currentAge: 32,
  retirementAge: 55,
};

describe('SmartDashboard', () => {
  it('renders without crashing', () => {
    render(
      <SmartDashboard
        userProgress={mockUserProgress}
        financialData={mockFinancialData}
        darkMode={false}
      />
    );
    
    expect(screen.getByText('🚀 Your FIRE Journey')).toBeInTheDocument();
  });

  it('displays correct completion percentage', () => {
    render(
      <SmartDashboard
        userProgress={mockUserProgress}
        financialData={mockFinancialData}
        darkMode={false}
      />
    );
    
    expect(screen.getByText('85% Complete')).toBeInTheDocument();
  });

  it('calls onNavigate when quick action buttons are clicked', () => {
    const mockOnNavigate = vi.fn();
    
    render(
      <SmartDashboard
        userProgress={mockUserProgress}
        financialData={mockFinancialData}
        darkMode={false}
        onNavigate={mockOnNavigate}
      />
    );
    
    // Click "Add Income" button
    const addIncomeButton = screen.getByText('Add Income');
    fireEvent.click(addIncomeButton);
    
    expect(mockOnNavigate).toHaveBeenCalledWith('budget', 'income');
  });

  it('calls onNavigate when insight card buttons are clicked', () => {
    const mockOnNavigate = vi.fn();
    
    render(
      <SmartDashboard
        userProgress={mockUserProgress}
        financialData={mockFinancialData}
        darkMode={false}
        onNavigate={mockOnNavigate}
      />
    );
    
    // Click "Optimize Now" button
    const optimizeButton = screen.getByText('Optimize Now');
    fireEvent.click(optimizeButton);
    
    expect(mockOnNavigate).toHaveBeenCalledWith('swiss', 'pillar-3a');
  });

  it('shows correct next action for incomplete profile', () => {
    const incompleteProgress = {
      ...mockUserProgress,
      hasBasicInfo: false,
      completionPercentage: 20,
    };
    
    const mockOnShowOnboarding = vi.fn();
    
    render(
      <SmartDashboard
        userProgress={incompleteProgress}
        financialData={mockFinancialData}
        darkMode={false}
        onShowOnboarding={mockOnShowOnboarding}
      />
    );
    
    // Should show "Get Started" action
    const getStartedButton = screen.getByText('Get Started');
    expect(getStartedButton).toBeInTheDocument();
    
    // Click should trigger onboarding
    fireEvent.click(getStartedButton);
    expect(mockOnShowOnboarding).toHaveBeenCalled();
  });

  it('shows correct next action for low savings rate', () => {
    const lowSavingsData = {
      ...mockFinancialData,
      savingsRate: 5, // Low savings rate
    };
    
    const mockOnNavigate = vi.fn();
    
    render(
      <SmartDashboard
        userProgress={mockUserProgress}
        financialData={lowSavingsData}
        darkMode={false}
        onNavigate={mockOnNavigate}
      />
    );
    
    // Should show "Optimize" action
    const optimizeButton = screen.getByText('Optimize');
    expect(optimizeButton).toBeInTheDocument();
    
    // Click should navigate to budget optimization
    fireEvent.click(optimizeButton);
    expect(mockOnNavigate).toHaveBeenCalledWith('budget', 'optimization');
  });

  it('displays financial metrics correctly', () => {
    render(
      <SmartDashboard
        userProgress={mockUserProgress}
        financialData={mockFinancialData}
        darkMode={false}
      />
    );
    
    // Check if financial metrics are displayed
    expect(screen.getByText('CHF 8,000')).toBeInTheDocument(); // Monthly income
    expect(screen.getByText('37.5%')).toBeInTheDocument(); // Savings rate
    expect(screen.getByText('25%')).toBeInTheDocument(); // FIRE progress
  });

  it('renders in dark mode correctly', () => {
    const { container } = render(
      <SmartDashboard
        userProgress={mockUserProgress}
        financialData={mockFinancialData}
        darkMode={true}
      />
    );
    
    // Check for dark mode classes
    const dashboard = container.querySelector('.smart-dashboard');
    expect(dashboard).toBeInTheDocument();
  });

  it('handles missing optional props gracefully', () => {
    render(
      <SmartDashboard
        userProgress={mockUserProgress}
        financialData={mockFinancialData}
        darkMode={false}
        // No onNavigate or onShowOnboarding props
      />
    );
    
    // Should render without errors
    expect(screen.getByText('🚀 Your FIRE Journey')).toBeInTheDocument();
    
    // Clicking buttons should not throw errors
    const addIncomeButton = screen.getByText('Add Income');
    expect(() => fireEvent.click(addIncomeButton)).not.toThrow();
  });
});
