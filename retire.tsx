import * as d3 from "d3";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react"; // Added useMemo and useCallback
import { useTranslation } from "react-i18next";
import useNumberFormat from "./src/hooks/useNumberFormat";
import OnboardingWizard from "./src/components/improved/OnboardingWizard";
import SmartDashboard from "./src/components/improved/SmartDashboard";

// Import LanguageSwitcher component
const LanguageSwitcher = ({ darkMode = false, className = "" }) => {
  const { i18n, t } = useTranslation("common");
  const [isOpen, setIsOpen] = useState(false);

  const SUPPORTED_LANGUAGES = [
    { code: "en", name: "English", nativeName: "English", flag: "🇬🇧" },
    { code: "de", name: "German", nativeName: "Deutsch", flag: "🇩🇪" },
  ];

  const currentLanguage =
    SUPPORTED_LANGUAGES.find((lang) => lang.code === i18n.language) ||
    SUPPORTED_LANGUAGES[0];

  const handleLanguageChange = async (languageCode) => {
    try {
      await i18n.changeLanguage(languageCode);
      setIsOpen(false);
    } catch (error) {
      console.error("Failed to change language:", error);
    }
  };

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
          darkMode
            ? "bg-gray-700 hover:bg-gray-600 text-white border border-gray-600"
            : "bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-sm"
        } focus:outline-none focus:ring-2 focus:ring-blue-500`}
        aria-label={t("language.switch")}
      >
        <span className="text-lg">{currentLanguage.flag}</span>
        <span className="text-sm font-medium hidden sm:inline">
          {currentLanguage.nativeName}
        </span>
        <svg
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {isOpen && (
        <div
          className={`absolute right-0 mt-2 w-48 rounded-lg shadow-lg z-20 ${
            darkMode
              ? "bg-gray-800 border border-gray-600"
              : "bg-white border border-gray-200"
          }`}
        >
          <div className="py-1">
            {SUPPORTED_LANGUAGES.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                className={`w-full flex items-center space-x-3 px-4 py-2 text-left transition-colors duration-150 ${
                  darkMode
                    ? "hover:bg-gray-700 text-white"
                    : "hover:bg-gray-50 text-gray-700"
                } ${
                  currentLanguage.code === language.code
                    ? darkMode
                      ? "bg-gray-700 text-blue-400"
                      : "bg-blue-50 text-blue-600"
                    : ""
                }`}
              >
                <span className="text-lg">{language.flag}</span>
                <div className="flex-1">
                  <div className="text-sm font-medium">
                    {language.nativeName}
                  </div>
                  <div
                    className={`text-xs ${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  >
                    {language.name}
                  </div>
                </div>
                {currentLanguage.code === language.code && (
                  <svg
                    className="w-4 h-4 text-blue-500"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Advanced Monte Carlo Simulation Engine
const MonteCarloEngine = {
  // Generate random returns based on historical volatility
  generateRandomReturn(meanReturn, volatility, distribution = "normal") {
    if (distribution === "normal") {
      // Box-Muller transformation for normal distribution
      const u1 = Math.random();
      const u2 = Math.random();
      const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
      return meanReturn + volatility * z0;
    } else if (distribution === "lognormal") {
      // Log-normal distribution for more realistic market returns
      const normal = this.generateRandomReturn(0, 1, "normal");
      return Math.exp(Math.log(meanReturn) + volatility * normal);
    }
    return meanReturn;
  },

  // Run Monte Carlo simulation for FIRE projections
  runSimulation(params, iterations = 1000) {
    const {
      currentAge,
      retirementAge,
      currentSavings,
      monthlyContribution,
      targetAmount,
      expectedReturn,
      volatility,
      inflationRate,
      inflationVolatility,
      economicShocks,
    } = params;

    const results: any[] = [];
    const yearsToRetirement = retirementAge - currentAge;

    for (let i = 0; i < iterations; i++) {
      let balance = currentSavings;
      let realBalance = currentSavings;
      let monthlyContrib = monthlyContribution;
      let cumulativeInflation = 1;

      const yearlyResults: any[] = [];

      for (let year = 0; year < yearsToRetirement; year++) {
        // Generate random returns and inflation
        const annualReturn = this.generateRandomReturn(
          expectedReturn / 100,
          volatility / 100,
          "lognormal"
        );
        const annualInflation = this.generateRandomReturn(
          inflationRate / 100,
          inflationVolatility / 100,
          "normal"
        );

        // Apply economic shocks (market crashes, recessions)
        let shockMultiplier = 1;
        if (economicShocks && Math.random() < economicShocks.probability) {
          shockMultiplier = 1 + economicShocks.severity; // Negative severity for crashes
        }

        const adjustedReturn = annualReturn * shockMultiplier;

        // Calculate year-end balance
        const yearStartBalance = balance;
        const annualContributions = monthlyContrib * 12;

        // Apply returns to average balance throughout year
        const avgBalance = yearStartBalance + annualContributions / 2;
        const investmentGrowth = avgBalance * adjustedReturn;

        balance = yearStartBalance + annualContributions + investmentGrowth;

        // Track real purchasing power
        cumulativeInflation *= 1 + annualInflation;
        realBalance = balance / cumulativeInflation;

        // Adjust future contributions for inflation
        monthlyContrib *= 1 + annualInflation;

        yearlyResults.push({
          year: currentAge + year + 1,
          nominalBalance: balance,
          realBalance: realBalance,
          annualReturn: adjustedReturn * 100,
          inflation: annualInflation * 100,
          monthlyContribution: monthlyContrib,
        });
      }

      const finalBalance = balance;
      const finalRealBalance = realBalance;
      const success = finalRealBalance >= targetAmount;
      const shortfall = success ? 0 : targetAmount - finalRealBalance;

      results.push({
        iteration: i + 1,
        finalBalance,
        finalRealBalance,
        success,
        shortfall,
        yearlyResults,
      });
    }

    return this.analyzeResults(results, targetAmount);
  },

  // Analyze Monte Carlo results
  analyzeResults(results, targetAmount) {
    const successfulRuns = results.filter((r) => r.success);
    const successRate = (successfulRuns.length / results.length) * 100;

    const finalBalances = results.map((r) => r.finalRealBalance);
    finalBalances.sort((a, b) => a - b);

    const percentiles = {
      p10: finalBalances[Math.floor(results.length * 0.1)],
      p25: finalBalances[Math.floor(results.length * 0.25)],
      p50: finalBalances[Math.floor(results.length * 0.5)],
      p75: finalBalances[Math.floor(results.length * 0.75)],
      p90: finalBalances[Math.floor(results.length * 0.9)],
    };

    const averageBalance =
      finalBalances.reduce((sum, bal) => sum + bal, 0) / finalBalances.length;
    const shortfalls = results
      .filter((r) => !r.success)
      .map((r) => r.shortfall);
    const averageShortfall =
      shortfalls.length > 0
        ? shortfalls.reduce((sum, s) => sum + s, 0) / shortfalls.length
        : 0;

    // Risk metrics
    const worstCase = finalBalances[0];
    const bestCase = finalBalances[finalBalances.length - 1];
    const volatilityOfOutcomes = this.calculateStandardDeviation(finalBalances);

    return {
      successRate,
      percentiles,
      averageBalance,
      averageShortfall,
      worstCase,
      bestCase,
      volatilityOfOutcomes,
      targetAmount,
      iterations: results.length,
      detailedResults: results,
      riskMetrics: {
        probabilityOfRuin:
          ((results.length - successfulRuns.length) / results.length) * 100,
        expectedShortfall: averageShortfall,
        valueAtRisk: targetAmount - percentiles.p10, // 10% chance of losing this much
        conditionalValueAtRisk: targetAmount - percentiles.p10, // Use p10 instead of p5
      },
    };
  },

  // Calculate standard deviation
  calculateStandardDeviation(values) {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map((val) => Math.pow(val - mean, 2));
    const avgSquaredDiff =
      squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  },

  // Stress testing scenarios
  generateStressTestScenarios() {
    return {
      baseCase: {
        name: "Base Case",
        expectedReturn: 6.0,
        volatility: 15.0,
        inflationRate: 2.0,
        inflationVolatility: 1.0,
        economicShocks: null,
      },
      bearMarket: {
        name: "Bear Market",
        expectedReturn: 4.0,
        volatility: 25.0,
        inflationRate: 1.0,
        inflationVolatility: 1.5,
        economicShocks: { probability: 0.15, severity: -0.3 },
      },
      highInflation: {
        name: "High Inflation",
        expectedReturn: 7.0,
        volatility: 20.0,
        inflationRate: 4.0,
        inflationVolatility: 2.0,
        economicShocks: { probability: 0.1, severity: -0.2 },
      },
      recession: {
        name: "Recession Scenario",
        expectedReturn: 3.0,
        volatility: 30.0,
        inflationRate: 0.5,
        inflationVolatility: 2.0,
        economicShocks: { probability: 0.25, severity: -0.4 },
      },
      stagflation: {
        name: "Stagflation",
        expectedReturn: 2.0,
        volatility: 25.0,
        inflationRate: 5.0,
        inflationVolatility: 3.0,
        economicShocks: { probability: 0.2, severity: -0.25 },
      },
    };
  },

  // Safe withdrawal rate analysis
  calculateSafeWithdrawalRate(balance, years = 30, successRate = 95) {
    const scenarios = this.generateStressTestScenarios();
    const withdrawalRates = [];

    for (let rate = 0.02; rate <= 0.06; rate += 0.002) {
      let totalSuccess = 0;
      let totalRuns = 0;

      Object.values(scenarios).forEach((scenario: any) => {
        const results = this.runWithdrawalSimulation(
          {
            initialBalance: balance,
            withdrawalRate: rate,
            years: years,
            expectedReturn: scenario.expectedReturn,
            volatility: scenario.volatility,
            inflationRate: scenario.inflationRate,
          },
          200
        );

        totalSuccess += results.successfulYears;
        totalRuns += results.totalRuns;
      });

      const overallSuccessRate = (totalSuccess / totalRuns) * 100;
      (withdrawalRates as any[]).push({
        rate: rate * 100,
        successRate: overallSuccessRate,
      });

      if (overallSuccessRate >= successRate) {
        return {
          safeWithdrawalRate: rate * 100,
          confidence: overallSuccessRate,
          analysis: withdrawalRates,
        };
      }
    }

    return {
      safeWithdrawalRate: 2.0, // Conservative fallback
      confidence: 90,
      analysis: withdrawalRates,
    };
  },

  // Withdrawal phase simulation
  runWithdrawalSimulation(params, iterations = 500) {
    const {
      initialBalance,
      withdrawalRate,
      years,
      expectedReturn,
      volatility,
      inflationRate,
    } = params;

    let successfulRuns = 0;

    for (let i = 0; i < iterations; i++) {
      let balance = initialBalance;
      let annualWithdrawal = initialBalance * withdrawalRate;
      let success = true;

      for (let year = 0; year < years; year++) {
        // Generate random return
        const annualReturn = this.generateRandomReturn(
          expectedReturn / 100,
          volatility / 100,
          "lognormal"
        );

        // Apply withdrawal at beginning of year
        balance -= annualWithdrawal;

        // Apply investment returns
        balance *= 1 + annualReturn;

        // Adjust withdrawal for inflation
        annualWithdrawal *= 1 + inflationRate / 100;

        // Check if we've run out of money
        if (balance <= 0) {
          success = false;
          break;
        }
      }

      if (success) successfulRuns++;
    }

    return {
      successfulYears: successfulRuns,
      totalRuns: iterations,
      successRate: (successfulRuns / iterations) * 100,
    };
  },
};

// Real-Time Economic Data Integration Engine
const SwissEconomicDataService = {
  // Cache for economic data
  cache: {
    snbData: null,
    marketData: null,
    inflationData: null,
    lastUpdate: null,
    cacheExpiry: 24 * 60 * 60 * 1000, // 24 hours
  },

  // Swiss National Bank (SNB) Data Integration
  async fetchSNBData() {
    try {
      // In production, this would connect to SNB API
      // For now, we'll use realistic current data with periodic updates
      const mockSNBData = {
        policyRate: 1.75, // Current SNB policy rate (Dec 2024)
        inflationTarget: 2.0, // SNB inflation target
        currentInflation: 1.4, // Current Swiss CPI inflation
        chfStrengthIndex: 0.92, // CHF strength vs EUR
        economicGrowthForecast: 1.2, // GDP growth forecast
        unemploymentRate: 2.1, // Swiss unemployment rate
        lastUpdated: new Date().toISOString(),
        confidence: "high", // Data confidence level
        source: "SNB Official Data",
      };

      this.cache.snbData = mockSNBData;
      this.cache.lastUpdate = Date.now();
      return mockSNBData;
    } catch (error) {
      console.error("Failed to fetch SNB data:", error);
      return this.getDefaultSNBData();
    }
  },

  // SIX Swiss Exchange Market Data
  async fetchMarketData() {
    try {
      // Mock current Swiss market data
      const mockMarketData = {
        smiIndex: 11847, // Current SMI level
        smiChange: 0.8, // Daily change %
        spiIndex: 15234, // Swiss Performance Index
        spiYearReturn: 6.2, // YTD return %
        bondYield10Y: 0.68, // 10-year Swiss government bond yield
        bondYield2Y: 0.45, // 2-year Swiss government bond yield
        volatilityIndex: 16.2, // Swiss market volatility
        marketSentiment: "neutral", // bullish/neutral/bearish
        lastUpdated: new Date().toISOString(),
        source: "SIX Swiss Exchange",
      };

      this.cache.marketData = mockMarketData;
      return mockMarketData;
    } catch (error) {
      console.error("Failed to fetch market data:", error);
      return this.getDefaultMarketData();
    }
  },

  // Swiss Inflation and Economic Indicators
  async fetchInflationData() {
    try {
      const mockInflationData = {
        currentCPI: 1.4, // Current consumer price index change
        coreCPI: 1.2, // Core inflation (excluding energy/food)
        housingCosts: 2.1, // Housing cost inflation
        healthcareCosts: 3.2, // Healthcare cost inflation
        energyCosts: -2.1, // Energy cost change
        foodCosts: 0.8, // Food cost inflation
        forecast12M: 1.6, // 12-month inflation forecast
        forecast24M: 1.8, // 24-month inflation forecast
        lastUpdated: new Date().toISOString(),
        source: "Federal Statistical Office",
      };

      this.cache.inflationData = mockInflationData;
      return mockInflationData;
    } catch (error) {
      console.error("Failed to fetch inflation data:", error);
      return this.getDefaultInflationData();
    }
  },

  // Get all economic data with caching
  async getAllEconomicData() {
    const now = Date.now();
    const cacheValid =
      this.cache.lastUpdate &&
      now - this.cache.lastUpdate < this.cache.cacheExpiry;

    if (
      cacheValid &&
      this.cache.snbData &&
      this.cache.marketData &&
      this.cache.inflationData
    ) {
      return {
        snb: this.cache.snbData,
        market: this.cache.marketData,
        inflation: this.cache.inflationData,
        cached: true,
      };
    }

    // Fetch fresh data
    const [snbData, marketData, inflationData] = await Promise.all([
      this.fetchSNBData(),
      this.fetchMarketData(),
      this.fetchInflationData(),
    ]);

    return {
      snb: snbData,
      market: marketData,
      inflation: inflationData,
      cached: false,
    };
  },

  // Calculate dynamic return assumptions based on current market conditions
  calculateDynamicReturns(
    assetAllocation = { stocks: 70, bonds: 25, cash: 5 }
  ) {
    const economicData = this.cache;
    if (!economicData.snbData || !economicData.marketData) {
      return this.getDefaultReturns();
    }

    const snb = economicData.snbData;
    const market = economicData.marketData;

    // Base returns adjusted for current conditions
    const stockReturn = this.calculateStockReturn(market, snb);
    const bondReturn = this.calculateBondReturn(market, snb);
    const cashReturn = snb.policyRate * 0.8; // Cash returns slightly below policy rate

    // Weighted portfolio return
    const portfolioReturn =
      (assetAllocation.stocks / 100) * stockReturn +
      (assetAllocation.bonds / 100) * bondReturn +
      (assetAllocation.cash / 100) * cashReturn;

    return {
      stocks: stockReturn,
      bonds: bondReturn,
      cash: cashReturn,
      portfolio: portfolioReturn,
      inflation: snb.currentInflation,
      realReturn: portfolioReturn - snb.currentInflation,
      confidence: this.calculateConfidence(market, snb),
      lastUpdated: new Date().toISOString(),
    };
  },

  // Calculate stock return expectations
  calculateStockReturn(market, snb) {
    const baseReturn = 7.0; // Historical Swiss stock market average

    // Adjust for current market conditions
    let adjustment = 0;

    // Market valuation adjustment
    if (market.spiYearReturn > 15) adjustment -= 1.0; // Overvalued market
    else if (market.spiYearReturn < -10) adjustment += 1.5; // Undervalued market

    // Volatility adjustment
    if (market.volatilityIndex > 25) adjustment -= 0.5; // High volatility
    else if (market.volatilityIndex < 15) adjustment += 0.3; // Low volatility

    // Economic growth adjustment
    if (snb.economicGrowthForecast > 2.0) adjustment += 0.5;
    else if (snb.economicGrowthForecast < 0.5) adjustment -= 1.0;

    return Math.max(3.0, Math.min(12.0, baseReturn + adjustment));
  },

  // Calculate bond return expectations
  calculateBondReturn(market, snb) {
    const baseReturn = market.bondYield10Y + 0.5; // Yield plus small premium

    // Adjust for interest rate environment
    let adjustment = 0;

    if (snb.policyRate > 2.0) adjustment += 0.3; // Rising rate environment
    else if (snb.policyRate < 0.5) adjustment -= 0.2; // Low rate environment

    return Math.max(0.5, Math.min(6.0, baseReturn + adjustment));
  },

  // Calculate confidence level in projections
  calculateConfidence(market, snb) {
    let confidence = 100;

    // Reduce confidence for high volatility
    if (market.volatilityIndex > 25) confidence -= 20;
    if (market.volatilityIndex > 35) confidence -= 20;

    // Reduce confidence for extreme economic conditions
    if (snb.currentInflation > 4.0 || snb.currentInflation < -1.0)
      confidence -= 15;
    if (snb.economicGrowthForecast < -1.0) confidence -= 25;

    return Math.max(50, confidence);
  },

  // Economic alert system
  checkAlertThresholds(userProfile: any) {
    const alerts: any[] = [];
    const economicData = this.cache;

    if (!economicData.snbData || !economicData.inflationData) return alerts;

    const snb = economicData.snbData;
    const inflation = economicData.inflationData;

    // Inflation alerts
    if (inflation.currentCPI > 3.0) {
      alerts.push({
        type: "inflation_high",
        severity: "warning",
        title: "High Inflation Alert",
        message: `Swiss inflation at ${inflation.currentCPI}% exceeds 3% threshold`,
        impact:
          "Consider adjusting your return assumptions and increasing equity allocation",
        action:
          "Review your FIRE projections with higher inflation assumptions",
      });
    }

    if (inflation.currentCPI < 0) {
      alerts.push({
        type: "deflation",
        severity: "info",
        title: "Deflation Detected",
        message: `Swiss inflation at ${inflation.currentCPI}% indicates deflationary pressure`,
        impact:
          "Your purchasing power may increase, but investment returns may be lower",
        action: "Consider more conservative return assumptions",
      });
    }

    // Interest rate alerts
    if (snb.policyRate !== userProfile.lastKnownPolicyRate) {
      const direction =
        snb.policyRate > (userProfile.lastKnownPolicyRate || 0)
          ? "increased"
          : "decreased";
      alerts.push({
        type: "policy_rate_change",
        severity: "info",
        title: "SNB Policy Rate Change",
        message: `SNB ${direction} policy rate to ${snb.policyRate}%`,
        impact:
          direction === "increased"
            ? "Higher bond yields, potential market volatility"
            : "Lower bond yields, potential asset price increases",
        action: "Consider rebalancing your portfolio allocation",
      });
    }

    // Market volatility alerts
    if (
      economicData.marketData &&
      economicData.marketData.volatilityIndex > 30
    ) {
      alerts.push({
        type: "high_volatility",
        severity: "warning",
        title: "High Market Volatility",
        message: `Swiss market volatility at ${economicData.marketData.volatilityIndex}% is elevated`,
        impact: "Increased uncertainty in short-term projections",
        action:
          "Consider stress-testing your FIRE plan with volatile market scenarios",
      });
    }

    return alerts;
  },

  // Default fallback data
  getDefaultSNBData() {
    return {
      policyRate: 1.75,
      inflationTarget: 2.0,
      currentInflation: 1.5,
      chfStrengthIndex: 0.92,
      economicGrowthForecast: 1.0,
      unemploymentRate: 2.2,
      lastUpdated: new Date().toISOString(),
      confidence: "medium",
      source: "Default Values",
    };
  },

  getDefaultMarketData() {
    return {
      smiIndex: 11800,
      smiChange: 0.0,
      spiIndex: 15200,
      spiYearReturn: 5.0,
      bondYield10Y: 0.7,
      bondYield2Y: 0.5,
      volatilityIndex: 18.0,
      marketSentiment: "neutral",
      lastUpdated: new Date().toISOString(),
      source: "Default Values",
    };
  },

  getDefaultInflationData() {
    return {
      currentCPI: 1.5,
      coreCPI: 1.3,
      housingCosts: 2.0,
      healthcareCosts: 3.0,
      energyCosts: 0.0,
      foodCosts: 1.0,
      forecast12M: 1.7,
      forecast24M: 1.9,
      lastUpdated: new Date().toISOString(),
      source: "Default Values",
    };
  },

  getDefaultReturns() {
    return {
      stocks: 7.0,
      bonds: 2.5,
      cash: 1.0,
      portfolio: 5.5,
      inflation: 1.5,
      realReturn: 4.0,
      confidence: 75,
      lastUpdated: new Date().toISOString(),
    };
  },
};

// Swiss Tax Optimization Engine
const SwissTaxEngine = {
  // Swiss Canton Data (2024 Tax Rates)
  cantons: {
    ZH: {
      name: "Zurich",
      federalMultiplier: 1.0,
      cantonalRate: 2.3,
      municipalMultiplier: 1.19,
      wealthTaxRate: 0.002,
      wealthTaxExemption: 70000,
    },
    BE: {
      name: "Bern",
      federalMultiplier: 1.0,
      cantonalRate: 3.06,
      municipalMultiplier: 1.54,
      wealthTaxRate: 0.0015,
      wealthTaxExemption: 50000,
    },
    LU: {
      name: "Lucerne",
      federalMultiplier: 1.0,
      cantonalRate: 2.6,
      municipalMultiplier: 1.5,
      wealthTaxRate: 0.0013,
      wealthTaxExemption: 75000,
    },
    UR: {
      name: "Uri",
      federalMultiplier: 1.0,
      cantonalRate: 2.2,
      municipalMultiplier: 1.2,
      wealthTaxRate: 0.001,
      wealthTaxExemption: 100000,
    },
    SZ: {
      name: "Schwyz",
      federalMultiplier: 1.0,
      cantonalRate: 2.17,
      municipalMultiplier: 0.8,
      wealthTaxRate: 0.001,
      wealthTaxExemption: 100000,
    },
    OW: {
      name: "Obwalden",
      federalMultiplier: 1.0,
      cantonalRate: 2.4,
      municipalMultiplier: 1.0,
      wealthTaxRate: 0.0012,
      wealthTaxExemption: 80000,
    },
    NW: {
      name: "Nidwalden",
      federalMultiplier: 1.0,
      cantonalRate: 1.9,
      municipalMultiplier: 0.9,
      wealthTaxRate: 0.001,
      wealthTaxExemption: 100000,
    },
    GL: {
      name: "Glarus",
      federalMultiplier: 1.0,
      cantonalRate: 2.8,
      municipalMultiplier: 1.25,
      wealthTaxRate: 0.0015,
      wealthTaxExemption: 60000,
    },
    ZG: {
      name: "Zug",
      federalMultiplier: 1.0,
      cantonalRate: 1.5,
      municipalMultiplier: 0.76,
      wealthTaxRate: 0.0005,
      wealthTaxExemption: 200000,
    },
    FR: {
      name: "Fribourg",
      federalMultiplier: 1.0,
      cantonalRate: 3.1,
      municipalMultiplier: 1.6,
      wealthTaxRate: 0.0018,
      wealthTaxExemption: 50000,
    },
    SO: {
      name: "Solothurn",
      federalMultiplier: 1.0,
      cantonalRate: 3.0,
      municipalMultiplier: 1.3,
      wealthTaxRate: 0.0016,
      wealthTaxExemption: 60000,
    },
    BS: {
      name: "Basel-Stadt",
      federalMultiplier: 1.0,
      cantonalRate: 3.0,
      municipalMultiplier: 1.0,
      wealthTaxRate: 0.002,
      wealthTaxExemption: 50000,
    },
    BL: {
      name: "Basel-Landschaft",
      federalMultiplier: 1.0,
      cantonalRate: 2.9,
      municipalMultiplier: 1.4,
      wealthTaxRate: 0.0018,
      wealthTaxExemption: 60000,
    },
    SH: {
      name: "Schaffhausen",
      federalMultiplier: 1.0,
      cantonalRate: 2.25,
      municipalMultiplier: 1.2,
      wealthTaxRate: 0.0012,
      wealthTaxExemption: 75000,
    },
    AR: {
      name: "Appenzell A.Rh.",
      federalMultiplier: 1.0,
      cantonalRate: 2.2,
      municipalMultiplier: 1.1,
      wealthTaxRate: 0.0013,
      wealthTaxExemption: 70000,
    },
    AI: {
      name: "Appenzell I.Rh.",
      federalMultiplier: 1.0,
      cantonalRate: 1.8,
      municipalMultiplier: 1.0,
      wealthTaxRate: 0.001,
      wealthTaxExemption: 100000,
    },
    SG: {
      name: "St. Gallen",
      federalMultiplier: 1.0,
      cantonalRate: 2.2,
      municipalMultiplier: 1.25,
      wealthTaxRate: 0.0014,
      wealthTaxExemption: 65000,
    },
    GR: {
      name: "Graubünden",
      federalMultiplier: 1.0,
      cantonalRate: 2.8,
      municipalMultiplier: 1.1,
      wealthTaxRate: 0.0013,
      wealthTaxExemption: 75000,
    },
    AG: {
      name: "Aargau",
      federalMultiplier: 1.0,
      cantonalRate: 2.3,
      municipalMultiplier: 1.3,
      wealthTaxRate: 0.0015,
      wealthTaxExemption: 60000,
    },
    TG: {
      name: "Thurgau",
      federalMultiplier: 1.0,
      cantonalRate: 2.2,
      municipalMultiplier: 1.2,
      wealthTaxRate: 0.0013,
      wealthTaxExemption: 70000,
    },
    TI: {
      name: "Ticino",
      federalMultiplier: 1.0,
      cantonalRate: 3.0,
      municipalMultiplier: 1.4,
      wealthTaxRate: 0.0018,
      wealthTaxExemption: 55000,
    },
    VD: {
      name: "Vaud",
      federalMultiplier: 1.0,
      cantonalRate: 3.54,
      municipalMultiplier: 1.6,
      wealthTaxRate: 0.002,
      wealthTaxExemption: 50000,
    },
    VS: {
      name: "Valais",
      federalMultiplier: 1.0,
      cantonalRate: 3.0,
      municipalMultiplier: 1.4,
      wealthTaxRate: 0.0016,
      wealthTaxExemption: 60000,
    },
    NE: {
      name: "Neuchâtel",
      federalMultiplier: 1.0,
      cantonalRate: 3.8,
      municipalMultiplier: 1.7,
      wealthTaxRate: 0.0022,
      wealthTaxExemption: 45000,
    },
    GE: {
      name: "Geneva",
      federalMultiplier: 1.0,
      cantonalRate: 3.2,
      municipalMultiplier: 1.45,
      wealthTaxRate: 0.002,
      wealthTaxExemption: 50000,
    },
    JU: {
      name: "Jura",
      federalMultiplier: 1.0,
      cantonalRate: 4.0,
      municipalMultiplier: 1.8,
      wealthTaxRate: 0.0025,
      wealthTaxExemption: 40000,
    },
  },

  // Federal Tax Brackets (2024)
  federalTaxBrackets: [
    { min: 0, max: 14500, rate: 0 },
    { min: 14500, max: 31600, baseRate: 0.0077, progressionFactor: 0.000165 },
    { min: 31600, max: 41400, baseRate: 0.0264, progressionFactor: 0.000275 },
    { min: 41400, max: 55200, baseRate: 0.0376, progressionFactor: 0.000275 },
    { min: 55200, max: 72500, baseRate: 0.066, progressionFactor: 0.000275 },
    { min: 72500, max: 78100, baseRate: 0.088, progressionFactor: 0.000275 },
    { min: 78100, max: Infinity, rate: 0.115 },
  ],

  // Calculate Federal Tax
  calculateFederalTax(income) {
    let tax = 0;

    for (const bracket of this.federalTaxBrackets) {
      if (income <= bracket.min) break;

      const taxableInBracket = Math.min(income, bracket.max) - bracket.min;

      if (bracket.rate !== undefined) {
        // Simple rate bracket
        tax += taxableInBracket * bracket.rate;
      } else {
        // Progressive bracket with base rate and progression factor
        const midPoint = (bracket.min + Math.min(income, bracket.max)) / 2;
        const effectiveRate =
          bracket.baseRate +
          (midPoint - bracket.min) * bracket.progressionFactor;
        tax += taxableInBracket * effectiveRate;
      }
    }

    return tax;
  },

  // Calculate Cantonal and Municipal Tax
  calculateCantonalTax(income, canton) {
    const cantonData = this.cantons[canton];
    if (!cantonData) return 0;

    const federalTax = this.calculateFederalTax(income);
    const cantonalTax = federalTax * cantonData.cantonalRate;
    const municipalTax = federalTax * cantonData.municipalMultiplier;

    return cantonalTax + municipalTax;
  },

  // Calculate Wealth Tax
  calculateWealthTax(netWorth, canton) {
    const cantonData = this.cantons[canton];
    if (!cantonData) return 0;

    const taxableWealth = Math.max(0, netWorth - cantonData.wealthTaxExemption);
    return taxableWealth * cantonData.wealthTaxRate;
  },

  // Calculate Total Tax Burden
  calculateTotalTax(income, netWorth, canton, civilStatus = "single") {
    const federalTax = this.calculateFederalTax(income);
    const cantonalTax = this.calculateCantonalTax(income, canton);
    const wealthTax = this.calculateWealthTax(netWorth, canton);

    // Apply civil status adjustments (married couples get reductions)
    const civilStatusMultiplier = civilStatus === "married" ? 0.85 : 1.0;

    const totalIncomeTax = (federalTax + cantonalTax) * civilStatusMultiplier;

    return {
      federalTax: federalTax * civilStatusMultiplier,
      cantonalTax: cantonalTax * civilStatusMultiplier,
      wealthTax,
      totalIncomeTax,
      totalTax: totalIncomeTax + wealthTax,
      effectiveRate: income > 0 ? (totalIncomeTax / income) * 100 : 0,
      marginalRate: this.calculateMarginalRate(income, canton, civilStatus),
    };
  },

  // Calculate Marginal Tax Rate (without circular dependency)
  calculateMarginalRate(income, canton, civilStatus = "single") {
    // Calculate taxes directly without calling calculateTotalTax to avoid recursion
    const civilStatusMultiplier = civilStatus === "married" ? 0.85 : 1.0;

    // Current income tax
    const currentFederalTax = this.calculateFederalTax(income);
    const currentCantonalTax = this.calculateCantonalTax(income, canton);
    const currentTotalIncomeTax =
      (currentFederalTax + currentCantonalTax) * civilStatusMultiplier;

    // Higher income tax (+1000)
    const higherFederalTax = this.calculateFederalTax(income + 1000);
    const higherCantonalTax = this.calculateCantonalTax(income + 1000, canton);
    const higherTotalIncomeTax =
      (higherFederalTax + higherCantonalTax) * civilStatusMultiplier;

    return ((higherTotalIncomeTax - currentTotalIncomeTax) / 1000) * 100;
  },

  // Pillar 3a Optimization
  optimizePillar3a(profile) {
    const { income, canton, civilStatus, currentAge, retirementAge } = profile;
    const pillar3aLimit = profile.hasSecondPillar
      ? 7056
      : Math.min(35280, income * 0.2);

    // Calculate tax savings from maximum contribution
    const currentTax = this.calculateTotalTax(income, 0, canton, civilStatus);
    const taxWithPillar3a = this.calculateTotalTax(
      income - pillar3aLimit,
      0,
      canton,
      civilStatus
    );
    const annualTaxSavings =
      currentTax.totalIncomeTax - taxWithPillar3a.totalIncomeTax;

    // Calculate optimal withdrawal strategy
    const yearsToRetirement = retirementAge - currentAge;
    const withdrawalYears = Math.min(5, Math.floor(yearsToRetirement / 5)); // Stagger withdrawals

    return {
      recommendedContribution: pillar3aLimit,
      annualTaxSavings,
      lifetimeTaxSavings: annualTaxSavings * yearsToRetirement,
      withdrawalStrategy: {
        numberOfAccounts: withdrawalYears,
        withdrawalYears: withdrawalYears,
        estimatedWithdrawalTax: pillar3aLimit * yearsToRetirement * 0.05, // Estimated 5% withdrawal tax
      },
    };
  },

  // Find Optimal Canton for Tax Efficiency
  findOptimalCanton(profile: any) {
    const { income, netWorth, civilStatus } = profile;
    const recommendations: any[] = [];

    Object.keys(this.cantons).forEach((cantonCode) => {
      const taxResult = this.calculateTotalTax(
        income,
        netWorth,
        cantonCode,
        civilStatus
      );
      const cantonData = this.cantons[cantonCode];

      recommendations.push({
        canton: cantonCode,
        name: cantonData.name,
        totalTax: taxResult.totalTax,
        effectiveRate: taxResult.effectiveRate,
        incomeTax: taxResult.totalIncomeTax,
        wealthTax: taxResult.wealthTax,
        savings: 0, // Will be calculated relative to current canton
      });
    });

    // Sort by total tax burden
    recommendations.sort((a: any, b: any) => a.totalTax - b.totalTax);

    // Calculate savings relative to highest tax canton
    const highestTax = recommendations[recommendations.length - 1].totalTax;
    recommendations.forEach((rec: any) => {
      rec.savings = highestTax - rec.totalTax;
    });

    return recommendations;
  },

  // Generate Tax Optimization Recommendations
  generateTaxOptimizations(profile: any) {
    const optimizations: any[] = [];
    const { income, netWorth, canton, currentAge, retirementAge } = profile;

    // Pillar 3a Optimization
    const pillar3aOpt = this.optimizePillar3a(profile);
    if (pillar3aOpt.annualTaxSavings > 0) {
      optimizations.push({
        type: "pillar3a",
        title: "Maximize Pillar 3a Contributions",
        description: `Contribute CHF ${pillar3aOpt.recommendedContribution.toLocaleString()} annually`,
        annualSavings: pillar3aOpt.annualTaxSavings,
        lifetimeSavings: pillar3aOpt.lifetimeTaxSavings,
        priority: "high",
        implementation:
          "Set up automatic monthly transfer of CHF " +
          Math.round(pillar3aOpt.recommendedContribution / 12).toLocaleString(),
      });
    }

    // Cantonal Relocation Analysis
    const cantonRecommendations = this.findOptimalCanton(profile);
    const currentCantonTax = cantonRecommendations.find(
      (r) => r.canton === canton
    );
    const bestCanton = cantonRecommendations[0];

    if (bestCanton.canton !== canton && bestCanton.savings > 2000) {
      optimizations.push({
        type: "relocation",
        title: `Consider Moving to ${bestCanton.name}`,
        description: `Potential annual tax savings of CHF ${Math.round(
          bestCanton.savings
        ).toLocaleString()}`,
        annualSavings: bestCanton.savings,
        lifetimeSavings: bestCanton.savings * (retirementAge - currentAge),
        priority: bestCanton.savings > 10000 ? "high" : "medium",
        implementation: `Research living costs and quality of life in ${bestCanton.name}`,
      });
    }

    // Wealth Tax Optimization
    if (netWorth > 500000) {
      const wealthTaxSavings = this.calculateWealthTaxOptimization(
        netWorth,
        canton
      );
      if (wealthTaxSavings.potentialSavings > 1000) {
        optimizations.push({
          type: "wealth_tax",
          title: "Optimize Wealth Tax Strategy",
          description: wealthTaxSavings.strategy,
          annualSavings: wealthTaxSavings.potentialSavings,
          lifetimeSavings: wealthTaxSavings.potentialSavings * 10, // Estimate 10 years
          priority: "medium",
          implementation: wealthTaxSavings.implementation,
        });
      }
    }

    return optimizations.sort(
      (a: any, b: any) => b.annualSavings - a.annualSavings
    );
  },

  // Wealth Tax Optimization Strategies
  calculateWealthTaxOptimization(netWorth, canton) {
    const cantonData = this.cantons[canton];
    const currentWealthTax = this.calculateWealthTax(netWorth, canton);

    // Strategy 1: Mortgage optimization
    const mortgageStrategy = Math.min(netWorth * 0.8, 1000000); // Max 80% LTV, 1M mortgage
    const netWorthWithMortgage = netWorth - mortgageStrategy;
    const wealthTaxWithMortgage = this.calculateWealthTax(
      netWorthWithMortgage,
      canton
    );
    const mortgageSavings = currentWealthTax - wealthTaxWithMortgage;

    // Strategy 2: Asset location optimization
    const assetLocationSavings = currentWealthTax * 0.2; // Estimate 20% savings through optimization

    const bestStrategy =
      mortgageSavings > assetLocationSavings
        ? {
            strategy: "Optimize mortgage debt to reduce taxable wealth",
            potentialSavings: mortgageSavings,
            implementation: `Consider maintaining mortgage debt of CHF ${mortgageStrategy.toLocaleString()} to reduce wealth tax`,
          }
        : {
            strategy: "Optimize asset location and structure",
            potentialSavings: assetLocationSavings,
            implementation:
              "Consider tax-efficient investment structures and asset location strategies",
          };

    return bestStrategy;
  },
};

// Data Persistence Service (Enhanced SPA approach)
const DataService = {
  // Auto-save functionality
  autoSave: (data, planName = "default") => {
    try {
      const saveData = {
        ...data,
        timestamp: new Date().toISOString(),
        version: "1.0",
      };
      localStorage.setItem(
        `swissBudgetPro_${planName}`,
        JSON.stringify(saveData)
      );
      localStorage.setItem("swissBudgetPro_lastSave", new Date().toISOString());
      return true;
    } catch (error) {
      console.error("Auto-save failed:", error);
      return false;
    }
  },

  // Load saved data
  loadPlan: (planName = "default") => {
    try {
      const saved = localStorage.getItem(`swissBudgetPro_${planName}`);
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      console.error("Load failed:", error);
      return null;
    }
  },

  // Get all saved plans
  getAllPlans: () => {
    const plans: any[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        key.startsWith("swissBudgetPro_") &&
        !key.includes("_lastSave") &&
        !key.includes("_snapshots")
      ) {
        const planName = key.replace("swissBudgetPro_", "");
        const data = DataService.loadPlan(planName);
        if (data) {
          plans.push({
            name: planName,
            timestamp: data.timestamp,
            data: data,
          });
        }
      }
    }
    return plans.sort(
      (a: any, b: any) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  },

  // Delete a plan
  deletePlan: (planName) => {
    localStorage.removeItem(`swissBudgetPro_${planName}`);
    localStorage.removeItem(`swissBudgetPro_snapshots_${planName}`);
  },

  // Historical snapshots
  saveSnapshot: (data, planName = "default") => {
    try {
      const snapshotKey = `swissBudgetPro_snapshots_${planName}`;
      const existing = JSON.parse(localStorage.getItem(snapshotKey) || "[]");

      const snapshot = {
        timestamp: new Date().toISOString(),
        totalMonthlyIncome: data.totalMonthlyIncome,
        totalExpenses: data.totalExpenses,
        totalSavings: data.totalSavings,
        savingsRate: data.savingsRate,
        netWorth:
          data.currentSavings +
          data.currentPensionLeavingBenefits +
          data.currentPillar3a,
        fireProgress: data.fireProgress || 0,
      };

      existing.push(snapshot);

      // Keep only last 24 months of snapshots
      const recentSnapshots = existing.slice(-24);
      localStorage.setItem(snapshotKey, JSON.stringify(recentSnapshots));

      return true;
    } catch (error) {
      console.error("Snapshot save failed:", error);
      return false;
    }
  },

  // Get historical snapshots
  getSnapshots: (planName = "default") => {
    try {
      const snapshotKey = `swissBudgetPro_snapshots_${planName}`;
      return JSON.parse(localStorage.getItem(snapshotKey) || "[]");
    } catch (error) {
      console.error("Snapshot load failed:", error);
      return [];
    }
  },

  // Export functionality
  exportToCsv: (data) => {
    const csvContent = [
      ["Metric", "Value"],
      ["Monthly Income", data.totalMonthlyIncome],
      ["Monthly Expenses", data.totalExpenses],
      ["Monthly Savings", data.totalSavings],
      ["Savings Rate (%)", data.savingsRate],
      ["Current Age", data.currentAge],
      ["Retirement Age", data.retirementAge],
      ["Years to Retirement", data.retirementAge - data.currentAge],
      ["FIRE Progress (%)", data.fireProgress || 0],
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `swiss-budget-pro-${
      new Date().toISOString().split("T")[0]
    }.csv`;
    a.click();
    URL.revokeObjectURL(url);
  },

  exportToJson: (data) => {
    const exportData = {
      ...data,
      exportDate: new Date().toISOString(),
      version: "1.0",
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `swiss-budget-pro-backup-${
      new Date().toISOString().split("T")[0]
    }.json`;
    a.click();
    URL.revokeObjectURL(url);
  },

  // Import functionality
  importFromJson: (file, callback) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const result = e.target?.result;
        if (typeof result === "string") {
          const data = JSON.parse(result);
          callback(data, null);
        } else {
          callback(null, "Invalid file format");
        }
      } catch (error) {
        callback(null, "Invalid JSON file");
      }
    };
    reader.readAsText(file);
  },
};

// Custom hook for localStorage with fallback to defaults
const useLocalStorage = (key: string, defaultValue: any, options: any = {}) => {
  const {
    serialize = JSON.stringify,
    deserialize = JSON.parse,
    debounceMs = 500,
  } = options;

  // Check if localStorage is available
  const isLocalStorageAvailable = () => {
    try {
      const testKey = "__localStorage_test__";
      localStorage.setItem(testKey, "test");
      localStorage.removeItem(testKey);
      return true;
    } catch {
      return false;
    }
  };

  // Initialize state with localStorage value or default
  const [storedValue, setStoredValue] = useState(() => {
    if (!isLocalStorageAvailable()) {
      return defaultValue;
    }

    try {
      const item = localStorage.getItem(key);
      return item ? deserialize(item) : defaultValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return defaultValue;
    }
  });

  // Debounced save function
  const debouncedSave = useCallback(
    (() => {
      let timeoutId;
      return (value) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          if (isLocalStorageAvailable()) {
            try {
              localStorage.setItem(key, serialize(value));
            } catch (error) {
              console.warn(`Error saving to localStorage key "${key}":`, error);
              // Handle quota exceeded or other errors gracefully
              if (error.name === "QuotaExceededError") {
                // Could implement cleanup strategy here
                console.warn("localStorage quota exceeded");
              }
            }
          }
        }, debounceMs);
      };
    })(),
    [key, serialize, debounceMs]
  );

  // Update both state and localStorage
  const setValue = useCallback(
    (value) => {
      try {
        // Allow value to be a function so we have the same API as useState
        const valueToStore =
          value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);
        debouncedSave(valueToStore);
      } catch (error) {
        console.warn(`Error setting value for key "${key}":`, error);
      }
    },
    [key, storedValue, debouncedSave]
  );

  return [storedValue, setValue];
};

// Utility to format currency
const formatCurrency = (amount) => {
  const validAmount =
    isNaN(amount) || amount === null || amount === undefined
      ? 0
      : Number(amount);
  return new Intl.NumberFormat("de-CH", {
    style: "currency",
    currency: "CHF",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(validAmount);
};

// Historical Chart Component
const HistoricalChart = ({
  darkMode,
  snapshots,
  formatCurrency,
}: {
  darkMode: boolean;
  snapshots: any[];
  formatCurrency: (value: number) => string;
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !snapshots || snapshots.length < 2) {
      d3.select(svgRef.current).selectAll("*").remove();
      return;
    }

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    const margin = { top: 20, right: 80, bottom: 40, left: 60 };
    const width = 600 - margin.left - margin.right;
    const height = 300 - margin.bottom - margin.top;

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Parse dates and sort data
    const data = snapshots
      .map((d: any) => ({
        ...d,
        date: new Date(d.timestamp),
      }))
      .sort((a: any, b: any) => a.date.getTime() - b.date.getTime());

    const xScale = d3
      .scaleTime()
      .domain(d3.extent(data, (d: any) => d.date) as [Date, Date])
      .range([0, width]);

    const yScale = d3
      .scaleLinear()
      .domain([
        0,
        d3.max(data, (d: any) =>
          Math.max(
            d.totalMonthlyIncome || 0,
            d.totalExpenses || 0,
            d.totalSavings || 0
          )
        ) || 0,
      ])
      .range([height, 0]);

    // Create line generators
    const lineIncome = d3
      .line<any>()
      .x((d: any) => xScale(d.date))
      .y((d: any) => yScale(d.totalMonthlyIncome || 0))
      .curve(d3.curveMonotoneX);

    const lineExpenses = d3
      .line<any>()
      .x((d: any) => xScale(d.date))
      .y((d: any) => yScale(d.totalExpenses || 0))
      .curve(d3.curveMonotoneX);

    const lineSavings = d3
      .line<any>()
      .x((d: any) => xScale(d.date))
      .y((d: any) => yScale(d.totalSavings || 0))
      .curve(d3.curveMonotoneX);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.timeFormat("%b %Y")))
      .selectAll("text")
      .style("fill", darkMode ? "#a0aec0" : "#4a5568");

    g.append("g")
      .call(d3.axisLeft(yScale).tickFormat((d) => formatCurrency(d)))
      .selectAll("text")
      .style("fill", darkMode ? "#a0aec0" : "#4a5568");

    // Add lines
    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#22c55e")
      .attr("stroke-width", 2)
      .attr("d", lineIncome);

    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#ef4444")
      .attr("stroke-width", 2)
      .attr("d", lineExpenses);

    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#3b82f6")
      .attr("stroke-width", 2)
      .attr("d", lineSavings);

    // Add legend
    const legend = g
      .append("g")
      .attr("transform", `translate(${width - 70}, 10)`);

    const legendData = [
      { label: "Income", color: "#22c55e" },
      { label: "Expenses", color: "#ef4444" },
      { label: "Savings", color: "#3b82f6" },
    ];

    legendData.forEach((item, i) => {
      const legendRow = legend
        .append("g")
        .attr("transform", `translate(0, ${i * 20})`);

      legendRow
        .append("rect")
        .attr("width", 12)
        .attr("height", 12)
        .attr("fill", item.color);

      legendRow
        .append("text")
        .attr("x", 16)
        .attr("y", 6)
        .attr("dy", "0.35em")
        .style("font-size", "12px")
        .style("fill", darkMode ? "#e2e8f0" : "#2d3748")
        .text(item.label);
    });
  }, [snapshots, darkMode, formatCurrency]);

  return (
    <div className="w-full flex justify-center">
      <svg ref={svgRef} width="600" height="300"></svg>
    </div>
  );
};

// D3.js Budget Donut Chart Component
const BudgetDonutChart = ({
  darkMode,
  totalExpenses,
  totalSavings,
  totalMonthlyIncome,
  essentialExpenses,
  nonEssentialExpenses,
  remaining,
  savingsRate,
}: {
  darkMode: boolean;
  totalExpenses: number;
  totalSavings: number;
  totalMonthlyIncome: number;
  essentialExpenses: number;
  nonEssentialExpenses: number;
  remaining: number;
  savingsRate: number;
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!svgRef.current || totalMonthlyIncome <= 0) {
      d3.select(svgRef.current).selectAll("*").remove();
      return;
    }

    const svg = d3.select(svgRef.current);
    const tooltip = d3.select(tooltipRef.current);
    svg.selectAll("*").remove();

    const width = 360;
    const height = 360;
    const margin = 20;
    const radius = Math.min(width, height) / 2 - margin;
    const innerRadius = radius * 0.6;

    const g = svg
      .append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);
    const data = [
      {
        label: "Essential Expenses",
        value: essentialExpenses,
        color: darkMode ? "#ef4444" : "#dc2626",
        icon: "🏠",
      },
      {
        label: "Discretionary Expenses",
        value: nonEssentialExpenses,
        color: darkMode ? "#f97316" : "#ea580c",
        icon: "🛍️",
      },
      {
        label: "Savings & Investments",
        value: totalSavings,
        color: darkMode ? "#22c55e" : "#16a34a",
        icon: "💎",
      },
      {
        label: "Available/Surplus",
        value: Math.max(0, remaining),
        color: darkMode ? "#3b82f6" : "#2563eb",
        icon: "💰",
      },
    ].filter((d) => d.value > 0);

    if (data.length === 0) return;

    const pie = d3
      .pie<any>()
      .value((d: any) => d.value)
      .sort(null);
    const arcDef = d3.arc<any>().innerRadius(innerRadius).outerRadius(radius);
    const hoverArcDef = d3
      .arc<any>()
      .innerRadius(innerRadius)
      .outerRadius(radius + 8);

    const arcs = g
      .selectAll(".arc")
      .data(pie(data))
      .enter()
      .append("g")
      .attr("class", "arc");

    arcs
      .append("path")
      .attr("d", (d: any) => arcDef(d))
      .attr("fill", (d: any) => d.data.color)
      .attr("stroke", darkMode ? "#1f2937" : "#ffffff")
      .attr("stroke-width", 2)
      .style("cursor", "pointer")
      .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))")
      .on("mouseover", function (event: any, d: any) {
        d3.select(this)
          .transition()
          .duration(150)
          .attr("d", hoverArcDef(d))
          .style("filter", "drop-shadow(0 4px 8px rgba(0,0,0,0.2))");
        tooltip.transition().duration(150).style("opacity", 1);
        const percentage = (d.data.value / totalMonthlyIncome) * 100;
        tooltip
          .html(
            `
          <div class="p-3 ${
            darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"
          } rounded-lg shadow-xl border max-w-xs text-sm">
            <div class="flex items-center mb-1.5"><span class="text-xl mr-1.5">${
              d.data.icon
            }</span><span class="font-bold">${d.data.label}</span></div>
            <div class="space-y-0.5">
              <div class="flex justify-between"><span>Amount:</span><span class="font-semibold">${formatCurrency(
                d.data.value
              )}</span></div>
              <div class="flex justify-between"><span>Percentage:</span><span class="font-semibold">${
                percentage > 0.01 ? percentage.toFixed(1) : "<0.1"
              }%</span></div>
              <div class="flex justify-between"><span>Annual:</span><span class="font-semibold">${formatCurrency(
                d.data.value * 12
              )}</span></div>
            </div>
          </div>`
          )
          .style("left", event.pageX + 15 + "px")
          .style("top", event.pageY - 15 + "px");
      })
      .on("mouseout", function () {
        d3.select(this)
          .transition()
          .duration(150)
          .attr("d", (d: any) => arcDef(d))
          .style("filter", "drop-shadow(0 2px 4px rgba(0,0,0,0.1))");
        tooltip.transition().duration(150).style("opacity", 0);
      });

    arcs
      .append("text")
      .attr("transform", (d: any) => `translate(${arcDef.centroid(d)})`)
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .style("fill", "white")
      .style("font-size", "12px")
      .style("font-weight", "bold")
      .style("text-shadow", "0px 0px 3px rgba(0,0,0,0.9)")
      .text((d: any) => {
        const p = (d.data.value / totalMonthlyIncome) * 100;
        return p >= 8 ? `${p.toFixed(0)}%` : "";
      });

    const centerGroup = g.append("g").attr("class", "center-text");
    centerGroup
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .attr("y", -8)
      .style("fill", darkMode ? "#ffffff" : "#1f2937")
      .style("font-size", "20px")
      .style("font-weight", "bold")
      .text(`${savingsRate > 0.01 ? savingsRate.toFixed(1) : "0.0"}%`);
    centerGroup
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .attr("y", 12)
      .style("fill", darkMode ? "#9ca3af" : "#6b7280")
      .style("font-size", "10px")
      .style("font-weight", "500")
      .text("Savings Rate");

    arcs
      .selectAll("path")
      .style("opacity", 0)
      .transition()
      .duration(800)
      .delay((d: any, i: number) => i * 150)
      .style("opacity", 1)
      .attrTween("d", function (d: any) {
        const i = d3.interpolate(
          { startAngle: d.startAngle, endAngle: d.startAngle },
          d
        );
        return (t: number) => arcDef(i(t)) || "";
      });
  }, [
    darkMode,
    totalExpenses,
    totalSavings,
    totalMonthlyIncome,
    essentialExpenses,
    nonEssentialExpenses,
    remaining,
    savingsRate,
  ]);

  return (
    <div className="relative flex justify-center">
      <svg
        ref={svgRef}
        width="360"
        height="360"
        className={`${darkMode ? "bg-gray-900/30" : "bg-white/30"} rounded-2xl`}
      ></svg>
      <div
        ref={tooltipRef}
        className="absolute pointer-events-none opacity-0 transition-opacity duration-150 z-20"
      ></div>
    </div>
  );
};

// Data Management Panel Component
const DataManagementPanel = ({
  darkMode,
  currentPlanName,
  setCurrentPlanName,
  onLoadPlan,
  onDeletePlan,
  onExport,
  onImport,
  lastSaved,
}: {
  darkMode: boolean;
  currentPlanName: string;
  setCurrentPlanName: (name: string) => void;
  onLoadPlan: (name: string) => void;
  onDeletePlan: (name: string) => void;
  onExport: (format: string) => void;
  onImport: (data: any) => void;
  lastSaved: string | null;
}) => {
  const [showPlans, setShowPlans] = useState(false);
  const [plans, setPlans] = useState<any[]>([]);
  const [newPlanName, setNewPlanName] = useState("");
  const [importFile, setImportFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const refreshPlans = () => {
    setPlans(DataService.getAllPlans());
  };

  useEffect(() => {
    refreshPlans();
  }, [showPlans]);

  const handleSaveAs = () => {
    if (newPlanName.trim()) {
      onLoadPlan(newPlanName.trim());
      setNewPlanName("");
      refreshPlans();
    }
  };

  const handleImport = () => {
    if (importFile) {
      DataService.importFromJson(importFile, (data, error) => {
        if (error) {
          alert("Import failed: " + error);
        } else {
          onImport(data);
          setImportFile(null);
          if (fileInputRef.current) fileInputRef.current.value = "";
        }
      });
    }
  };

  return (
    <div
      className={`p-4 rounded-lg ${
        darkMode ? "bg-gray-800/60" : "bg-white/80"
      } border ${darkMode ? "border-gray-600/40" : "border-gray-200"}`}
    >
      <div className="flex items-center justify-between mb-3">
        <h4
          className={`text-sm font-semibold ${
            darkMode ? "text-gray-200" : "text-gray-700"
          } flex items-center`}
        >
          <span className="mr-2">💾</span> Data Management
        </h4>
        <button
          onClick={() => setShowPlans(!showPlans)}
          className={`text-xs px-2 py-1 rounded ${
            darkMode ? "bg-blue-600 text-white" : "bg-blue-500 text-white"
          }`}
        >
          {showPlans ? "Hide" : "Show"} Plans
        </button>
      </div>

      {lastSaved && (
        <div
          className={`text-2xs mb-2 ${
            darkMode ? "text-green-400" : "text-green-600"
          }`}
        >
          ✅ Auto-saved: {new Date(lastSaved).toLocaleString()}
        </div>
      )}

      <div className="space-y-2">
        {/* Current Plan */}
        <div
          className={`text-xs ${darkMode ? "text-gray-300" : "text-gray-600"}`}
        >
          Current Plan: <span className="font-semibold">{currentPlanName}</span>
        </div>

        {/* Save As New Plan */}
        <div className="flex space-x-2">
          <input
            type="text"
            placeholder="New plan name"
            value={newPlanName}
            onChange={(e) => setNewPlanName(e.target.value)}
            className={`flex-1 px-2 py-1 text-xs rounded border ${
              darkMode
                ? "bg-gray-700 border-gray-500 text-white"
                : "bg-white border-gray-300"
            }`}
          />
          <button
            onClick={handleSaveAs}
            disabled={!newPlanName.trim()}
            className={`px-3 py-1 text-xs rounded ${
              darkMode ? "bg-green-600 text-white" : "bg-green-500 text-white"
            } disabled:opacity-50`}
          >
            Save As
          </button>
        </div>

        {/* Export/Import */}
        <div className="flex space-x-2">
          <button
            onClick={() => onExport("csv")}
            className={`px-3 py-1 text-xs rounded ${
              darkMode ? "bg-blue-600 text-white" : "bg-blue-500 text-white"
            }`}
          >
            Export CSV
          </button>
          <button
            onClick={() => onExport("json")}
            className={`px-3 py-1 text-xs rounded ${
              darkMode ? "bg-purple-600 text-white" : "bg-purple-500 text-white"
            }`}
          >
            Backup JSON
          </button>
        </div>

        {/* Import */}
        <div className="flex space-x-2">
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={(e) => setImportFile(e.target.files?.[0] || null)}
            className={`flex-1 text-2xs ${
              darkMode ? "text-gray-300" : "text-gray-600"
            }`}
          />
          <button
            onClick={handleImport}
            disabled={!importFile}
            className={`px-3 py-1 text-xs rounded ${
              darkMode ? "bg-orange-600 text-white" : "bg-orange-500 text-white"
            } disabled:opacity-50`}
          >
            Import
          </button>
        </div>

        {/* Saved Plans List */}
        {showPlans && (
          <div
            className={`mt-3 p-2 rounded ${
              darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
            } max-h-32 overflow-y-auto`}
          >
            {plans.length === 0 ? (
              <div
                className={`text-xs ${
                  darkMode ? "text-gray-400" : "text-gray-500"
                }`}
              >
                No saved plans
              </div>
            ) : (
              plans.map((plan) => (
                <div
                  key={plan.name}
                  className="flex items-center justify-between py-1"
                >
                  <div
                    className={`text-xs ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    <div className="font-medium">{plan.name}</div>
                    <div className="text-2xs opacity-70">
                      {new Date(plan.timestamp).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <button
                      onClick={() => {
                        onLoadPlan(plan.name);
                        setShowPlans(false);
                      }}
                      className={`px-2 py-0.5 text-2xs rounded ${
                        darkMode
                          ? "bg-blue-600 text-white"
                          : "bg-blue-500 text-white"
                      }`}
                    >
                      Load
                    </button>
                    <button
                      onClick={() => {
                        if (confirm(`Delete plan "${plan.name}"?`)) {
                          onDeletePlan(plan.name);
                          refreshPlans();
                        }
                      }}
                      className={`px-2 py-0.5 text-2xs rounded ${
                        darkMode
                          ? "bg-red-600 text-white"
                          : "bg-red-500 text-white"
                      }`}
                    >
                      Del
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// D3.js Projection Line Chart Component
const ProjectionChartCanvas = ({
  trajectoryData,
  darkMode,
  retirementAge,
  formatCurrency,
}: {
  trajectoryData: any[];
  darkMode: boolean;
  retirementAge: number;
  formatCurrency: (value: number) => string;
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [activeSeries, setActiveSeries] = useState({
    totalBalance: true,
    balance: true,
    pensionBalance: true,
    realBalance: false,
    totalContributions: false,
    interestEarned: false,
  });

  useEffect(() => {
    if (!svgRef.current || !trajectoryData || trajectoryData.length === 0) {
      d3.select(svgRef.current).selectAll("*").remove();
      return;
    }

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    const parentWidth = svgRef.current.parentElement.clientWidth;
    const margin = { top: 20, right: 20, bottom: 70, left: 70 };
    const width = Math.max(parentWidth - margin.left - margin.right, 250);
    const height = 400 - margin.top - margin.bottom;

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const xScale = d3
      .scaleLinear()
      .domain(d3.extent(trajectoryData, (d: any) => d.age) as [number, number])
      .range([0, width]);
    const maxYValue =
      d3.max(trajectoryData, (d: any) =>
        Math.max(
          d.totalBalance || 0,
          d.balance || 0,
          d.pensionBalance || 0,
          d.realBalance || 0,
          d.totalContributions || 0,
          d.interestEarned || 0
        )
      ) || 100000;
    const yScale = d3
      .scaleLinear()
      .domain([0, maxYValue > 0 ? maxYValue : 100000])
      .range([height, 0])
      .nice();

    const xAxis = d3
      .axisBottom(xScale)
      .tickFormat(d3.format("d"))
      .ticks(Math.min(trajectoryData.length, Math.floor(width / 60)));
    const yAxis = d3
      .axisLeft(yScale)
      .tickFormat((d) => `${formatCurrency(d / 1000)}k`)
      .ticks(5);

    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(xAxis)
      .selectAll("text")
      .style("fill", darkMode ? "#a0aec0" : "#4a5568");
    g.append("g")
      .call(yAxis)
      .selectAll("text")
      .style("fill", darkMode ? "#a0aec0" : "#4a5568");

    g.append("text")
      .attr("text-anchor", "middle")
      .attr("x", width / 2)
      .attr("y", height + margin.bottom - 30)
      .style("fill", darkMode ? "#cbd5e0" : "#2d3748")
      .style("font-size", "12px")
      .text("Age");
    g.append("text")
      .attr("text-anchor", "middle")
      .attr("transform", "rotate(-90)")
      .attr("y", -margin.left + 20)
      .attr("x", -height / 2)
      .style("fill", darkMode ? "#cbd5e0" : "#2d3748")
      .style("font-size", "12px")
      .text("Portfolio Value (CHF)");

    const lineDefinitions = {
      totalBalance: {
        generator: d3
          .line<any>()
          .x((d: any) => xScale(d.age))
          .y((d: any) => yScale(d.totalBalance || 0))
          .curve(d3.curveMonotoneX),
        color: darkMode ? "#8b5cf6" : "#7c3aed",
        label: "Total Balance",
      },
      balance: {
        generator: d3
          .line<any>()
          .x((d: any) => xScale(d.age))
          .y((d: any) => yScale(d.balance || 0))
          .curve(d3.curveMonotoneX),
        color: darkMode ? "#63b3ed" : "#4299e1",
        label: "General Savings",
      },
      pensionBalance: {
        generator: d3
          .line<any>()
          .x((d: any) => xScale(d.age))
          .y((d: any) => yScale(d.pensionBalance || 0))
          .curve(d3.curveMonotoneX),
        color: darkMode ? "#3b82f6" : "#1d4ed8",
        label: "Pension (🏛️)",
      },
      realBalance: {
        generator: d3
          .line<any>()
          .x((d: any) => xScale(d.age))
          .y((d: any) => yScale(d.realBalance || 0))
          .curve(d3.curveMonotoneX),
        color: darkMode ? "#68d391" : "#48bb78",
        label: "Real Balance (Today's CHF)",
      },
      totalContributions: {
        generator: d3
          .line<any>()
          .x((d: any) => xScale(d.age))
          .y((d: any) => yScale(d.totalContributions || 0))
          .curve(d3.curveMonotoneX),
        color: darkMode ? "#fc8181" : "#f56565",
        label: "Total Contributions",
      },
      interestEarned: {
        generator: d3
          .line<any>()
          .x((d: any) => xScale(d.age))
          .y((d: any) => yScale(d.interestEarned || 0))
          .curve(d3.curveMonotoneX),
        color: darkMode ? "#d6bcfa" : "#b794f4",
        label: "Interest Earned",
      },
    };

    Object.entries(lineDefinitions).forEach(([key, { generator, color }]) => {
      if (activeSeries[key]) {
        g.append("path")
          .datum(trajectoryData)
          .attr("fill", "none")
          .attr("stroke", color)
          .attr("stroke-width", 2.5)
          .attr("d", generator)
          .style("opacity", 0)
          .transition()
          .duration(1000)
          .delay(Object.keys(lineDefinitions).indexOf(key) * 100)
          .style("opacity", 1);
      }
    });

    const firstAge = trajectoryData[0]?.age;
    const lastAge = trajectoryData[trajectoryData.length - 1]?.age;
    if (
      firstAge !== undefined &&
      lastAge !== undefined &&
      retirementAge >= firstAge &&
      retirementAge <= lastAge
    ) {
      g.append("line")
        .attr("x1", xScale(retirementAge))
        .attr("x2", xScale(retirementAge))
        .attr("y1", 0)
        .attr("y2", height)
        .attr("stroke", darkMode ? "#fbd38d" : "#f6e05e")
        .attr("stroke-width", 2)
        .attr("stroke-dasharray", "4,4");
      g.append("text")
        .attr("x", xScale(retirementAge) + 5)
        .attr("y", 15)
        .attr("fill", darkMode ? "#fbd38d" : "#dd6b20")
        .style("font-size", "10px")
        .text("Retirement");
    }

    const legendContainer = svg
      .append("g")
      .attr(
        "transform",
        `translate(${margin.left}, ${height + margin.top + 30})`
      );

    const legendItems = legendContainer
      .selectAll(".legend-item")
      .data(Object.entries(lineDefinitions))
      .enter()
      .append("g")
      .attr("class", "legend-item")
      .attr(
        "transform",
        (d: any, i: number) =>
          `translate(${(i % 2) * (width / 2 - 20)}, ${Math.floor(i / 2) * 20})`
      )
      .style("cursor", "pointer")
      .on("click", (event: any, [key]: [string, any]) => {
        setActiveSeries((prev: any) => ({ ...prev, [key]: !prev[key] }));
      });

    legendItems
      .append("rect")
      .attr("x", 0)
      .attr("width", 14)
      .attr("height", 14)
      .style("fill", ([key, { color }]: [string, any]) => color)
      .style("opacity", ([key]: [string, any]) =>
        activeSeries[key] ? 1 : 0.5
      );

    legendItems
      .append("text")
      .attr("x", 20)
      .attr("y", 7)
      .attr("dy", ".35em")
      .style("text-anchor", "start")
      .style("font-size", "11px")
      .style("fill", darkMode ? "#e2e8f0" : "#2d3748")
      .style("opacity", ([key]: [string, any]) => (activeSeries[key] ? 1 : 0.5))
      .text(([key, { label }]: [string, any]) => label);
  }, [trajectoryData, darkMode, retirementAge, formatCurrency, activeSeries]);

  return (
    <div
      className={`w-full ${
        darkMode ? "bg-gray-800/50" : "bg-white/50"
      } p-3 md:p-4 rounded-2xl shadow-xl`}
    >
      <svg ref={svgRef} width="100%" height="450"></svg>
      <div
        ref={tooltipRef}
        className="absolute pointer-events-none opacity-0 transition-opacity duration-150 z-20 text-sm"
      ></div>
    </div>
  );
};

// Enhanced Error Boundary Component with Detailed Logging
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    const errorId = `error_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Enhanced error logging for debugging
    const errorDetails = {
      errorId,
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      errorInfo: {
        componentStack: errorInfo.componentStack,
      },
      userAgent: navigator.userAgent,
      url: window.location.href,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      localStorage: this.getLocalStorageSnapshot(),
      buildInfo: {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || "development",
      },
    };

    // Log to console with structured data
    console.group(`🚨 Swiss Budget Pro Error [${errorId}]`);
    console.error("Error Details:", errorDetails);
    console.error("Original Error:", error);
    console.error("Error Info:", errorInfo);
    console.groupEnd();

    // Store error details for potential debugging
    try {
      localStorage.setItem(
        `swissBudgetPro_error_${errorId}`,
        JSON.stringify(errorDetails)
      );
      // Keep only last 5 errors to avoid storage bloat
      this.cleanupOldErrors();
    } catch (storageError) {
      console.warn("Could not store error details:", storageError);
    }

    // Make error details available to tests
    if (window.Cypress || window.playwright) {
      window.lastError = errorDetails;
    }

    this.setState({
      error,
      errorInfo,
      errorId,
    });
  }

  getLocalStorageSnapshot() {
    try {
      const snapshot = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith("swissBudgetPro_")) {
          snapshot[key] = localStorage.getItem(key);
        }
      }
      return snapshot;
    } catch (e) {
      return { error: "Could not access localStorage" };
    }
  }

  cleanupOldErrors() {
    try {
      const errorKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith("swissBudgetPro_error_")) {
          errorKeys.push(key);
        }
      }

      // Sort by timestamp and keep only the 5 most recent
      errorKeys
        .sort()
        .slice(0, -5)
        .forEach((key) => {
          localStorage.removeItem(key);
        });
    } catch (e) {
      console.warn("Could not cleanup old errors:", e);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gray-900 text-white p-8 flex items-center justify-center">
          <div className="text-center max-w-2xl">
            <h1 className="text-4xl font-bold mb-4">🇨🇭 Swiss Budget Pro</h1>
            <p className="text-xl mb-4">
              Your Advanced Financial Command Center
            </p>
            <div className="bg-red-800/50 border border-red-600 rounded-lg p-6 mb-4">
              <h2 className="text-lg font-semibold mb-2">
                ⚠️ Application Error
              </h2>
              <p className="text-sm mb-4">
                The application encountered an error. Please try the recovery
                options below.
              </p>

              {/* Error details for debugging */}
              <details className="text-left text-xs bg-gray-800/50 p-3 rounded mb-4">
                <summary className="cursor-pointer font-medium mb-2">
                  🔍 Error Details (for debugging)
                </summary>
                <div className="space-y-2">
                  <div>
                    <strong>Error ID:</strong> {this.state.errorId}
                  </div>
                  <div>
                    <strong>Error:</strong> {this.state.error?.message}
                  </div>
                  <div>
                    <strong>Component:</strong>{" "}
                    {this.state.errorInfo?.componentStack
                      ?.split("\n")[1]
                      ?.trim()}
                  </div>
                  <div>
                    <strong>Timestamp:</strong> {new Date().toLocaleString()}
                  </div>
                  <div>
                    <strong>Stack:</strong>{" "}
                    <pre className="text-xs mt-1 whitespace-pre-wrap">
                      {this.state.error?.stack}
                    </pre>
                  </div>
                </div>
              </details>

              <div className="space-y-3">
                <button
                  onClick={() => window.location.reload()}
                  className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium transition-colors"
                >
                  🔄 Refresh Page
                </button>
                <button
                  onClick={() => {
                    // Clear only Swiss Budget Pro data, not all localStorage
                    const keys = Object.keys(localStorage).filter((key) =>
                      key.startsWith("swissBudgetPro_")
                    );
                    keys.forEach((key) => localStorage.removeItem(key));
                    window.location.reload();
                  }}
                  className="w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 rounded-lg font-medium transition-colors"
                >
                  🔧 Reset Data & Refresh
                </button>
                <button
                  onClick={() => {
                    const errorLog = {
                      errorId: this.state.errorId,
                      timestamp: new Date().toISOString(),
                      error: {
                        message: this.state.error?.message,
                        stack: this.state.error?.stack,
                        name: this.state.error?.name,
                      },
                      errorInfo: this.state.errorInfo,
                      userAgent: navigator.userAgent,
                      url: window.location.href,
                    };
                    const blob = new Blob([JSON.stringify(errorLog, null, 2)], {
                      type: "application/json",
                    });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement("a");
                    a.href = url;
                    a.download = `swiss-budget-pro-error-${this.state.errorId}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                  className="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg font-medium transition-colors"
                >
                  📋 Export Error Log
                </button>
              </div>
            </div>

            {/* Test-friendly error indicator */}
            <div
              data-testid="error-boundary"
              data-error-id={this.state.errorId}
              data-error-message={this.state.error?.message}
              className="hidden"
            />
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const SwissBudgetPro = () => {
  // i18n hooks
  const { t, i18n } = useTranslation([
    "common",
    "financial",
    "swiss",
    "forms",
    "reports",
    "errors",
  ]);
  const { formatCurrency, formatPercentage, formatNumber } = useNumberFormat();

  // Data Persistence State
  const [currentPlanName, setCurrentPlanName] = useState("default");
  const [lastSaved, setLastSaved] = useState(null);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);

  // Swiss Tax Optimization State
  const [selectedCanton, setSelectedCanton] = useLocalStorage(
    "swissBudgetPro_canton",
    "ZH"
  );
  const [civilStatus, setCivilStatus] = useLocalStorage(
    "swissBudgetPro_civilStatus",
    "single"
  );
  const [hasSecondPillar, setHasSecondPillar] = useLocalStorage(
    "swissBudgetPro_hasSecondPillar",
    true
  );
  const [currentPillar3a, setCurrentPillar3a] = useLocalStorage(
    "swissBudgetPro_currentPillar3a",
    "0"
  );

  // Economic Data Integration State
  const [economicData, setEconomicData] = useState(null);
  const [economicAlerts, setEconomicAlerts] = useState([]);
  const [useDynamicReturns, setUseDynamicReturns] = useLocalStorage(
    "swissBudgetPro_useDynamicReturns",
    true
  );
  const [lastKnownPolicyRate, setLastKnownPolicyRate] = useLocalStorage(
    "swissBudgetPro_lastKnownPolicyRate",
    1.75
  );

  // Monte Carlo Simulation State
  const [monteCarloResults, setMonteCarloResults] = useState(null);
  const [simulationRunning, setSimulationRunning] = useState(false);
  const [simulationIterations, setSimulationIterations] = useLocalStorage(
    "swissBudgetPro_simulationIterations",
    1000
  );
  const [stressTestScenario, setStressTestScenario] = useLocalStorage(
    "swissBudgetPro_stressTestScenario",
    "baseCase"
  );
  const [showAdvancedAnalytics, setShowAdvancedAnalytics] = useLocalStorage(
    "swissBudgetPro_showAdvancedAnalytics",
    false
  );

  // Swiss Relocation Calculator State
  const [relocationTargetCanton, setRelocationTargetCanton] = useLocalStorage(
    "swissBudgetPro_relocationTargetCanton",
    "ZG"
  );
  const [relocationProfile, setRelocationProfile] = useLocalStorage(
    "swissBudgetPro_relocationProfile",
    {
      hasJobOffer: false,
      isRemoteWorker: false,
      hasChildren: false,
      ownsProperty: false,
      monthlyRent: 2000,
    }
  );
  const [showAllCantons, setShowAllCantons] = useState(false);

  // localStorage-backed state with fallbacks to defaults
  const [darkMode, setDarkMode] = useLocalStorage(
    "swissBudgetPro_darkMode",
    true
  );
  const [monthlyIncome, setMonthlyIncome] = useLocalStorage(
    "swissBudgetPro_monthlyIncome",
    "10159.95"
  );
  const [incomePercentage, setIncomePercentage] = useLocalStorage(
    "swissBudgetPro_incomePercentage",
    100
  );
  const [companyIncome, setCompanyIncome] = useLocalStorage(
    "swissBudgetPro_companyIncome",
    "2500"
  );
  const [companyIncomeStartYear, setCompanyIncomeStartYear] = useLocalStorage(
    "swissBudgetPro_companyIncomeStartYear",
    2027
  );
  const [companyIncomeGrowthRate, setCompanyIncomeGrowthRate] = useLocalStorage(
    "swissBudgetPro_companyIncomeGrowthRate",
    3
  );
  const [hsluIncome, setHsluIncome] = useLocalStorage(
    "swissBudgetPro_hsluIncome",
    "800"
  );
  const [ruagIncome, setRuagIncome] = useLocalStorage(
    "swissBudgetPro_ruagIncome",
    "2300"
  );
  const [expenses, setExpenses] = useLocalStorage("swissBudgetPro_expenses", [
    {
      id: 1,
      category: "Housing (Rent/Mortgage)",
      amount: "2551",
      essential: true,
    },
    { id: 2, category: "Utilities", amount: "90", essential: true },
    { id: 3, category: "Food & Groceries", amount: "500", essential: true },
    { id: 4, category: "Transportation", amount: "250", essential: true },
    { id: 5, category: "Insurance", amount: "200", essential: true },
  ]);
  const [savings, setSavings] = useLocalStorage("swissBudgetPro_savings", [
    { id: 1, goal: "Emergency Fund", amount: "500" },
    { id: 2, goal: "Retirement (Pillar 3a)", amount: "1280" },
    { id: 3, goal: "Investment Portfolio", amount: "1000" },
  ]);
  const [currentAge, setCurrentAge] = useLocalStorage(
    "swissBudgetPro_currentAge",
    46
  );
  const [retirementAge, setRetirementAge] = useLocalStorage(
    "swissBudgetPro_retirementAge",
    55
  );
  const [currentSavings, setCurrentSavings] = useLocalStorage(
    "swissBudgetPro_currentSavings",
    "300000"
  );
  const [expectedReturn, setExpectedReturn] = useLocalStorage(
    "swissBudgetPro_expectedReturn",
    5
  );
  const [inflationRate, setInflationRate] = useLocalStorage(
    "swissBudgetPro_inflationRate",
    1.59
  );
  const [targetRetirementIncome, setTargetRetirementIncome] = useLocalStorage(
    "swissBudgetPro_targetRetirementIncome",
    "6000"
  );
  const [emergencyFundTargetMonths, setEmergencyFundTargetMonths] =
    useLocalStorage("swissBudgetPro_emergencyFundTargetMonths", 6);
  const [currentPensionLeavingBenefits, setCurrentPensionLeavingBenefits] =
    useLocalStorage(
      "swissBudgetPro_currentPensionLeavingBenefits",
      "408231.90"
    );
  const [basePensionContribution] = useState(1512.1); // Fixed base contribution when at 100% work time

  // Calculate monthly amounts from annual income sources
  const hsluIncomeAmount = (parseFloat(hsluIncome) || 0) / 12;
  const ruagIncomeAmount = (parseFloat(ruagIncome) || 0) / 12;

  // Current year and calculated start age for company income
  const currentYear = 2025;
  const companyIncomeStartAge = Math.max(
    currentAge,
    currentAge + (companyIncomeStartYear - currentYear)
  );

  // Calculate effective primary income based on work percentage
  const currentPrimaryIncome =
    (parseFloat(monthlyIncome) || 0) * (incomePercentage / 100);

  // Calculate adjusted pension contribution based on work time percentage
  const adjustedPensionContribution =
    basePensionContribution * (incomePercentage / 100);

  const initialSavingsSum = savings.reduce(
    (sum, s) => sum + (parseFloat(s.amount) || 0),
    0
  );
  const initialTotalIncome =
    currentPrimaryIncome +
    (currentYear >= companyIncomeStartYear
      ? parseFloat(companyIncome) || 0
      : 0) +
    hsluIncomeAmount +
    ruagIncomeAmount;
  const [targetOverallSavingsRate, setTargetOverallSavingsRate] =
    useLocalStorage(
      "swissBudgetPro_targetOverallSavingsRate",
      initialTotalIncome > 0
        ? (initialSavingsSum / initialTotalIncome) * 100
        : 20
    );

  const [pillar3aLimit] = useState(7056);
  const [activeTab, setActiveTab] = useLocalStorage(
    "swissBudgetPro_activeTab",
    "dashboard"
  );
  const [secondaryTab, setSecondaryTab] = useLocalStorage(
    "swissBudgetPro_secondaryTab",
    "taxOptimization"
  );
  const [showTargetAnalysis, setShowTargetAnalysis] = useLocalStorage(
    "swissBudgetPro_showTargetAnalysis",
    false
  );

  // Onboarding and User Progress State
  const [showOnboarding, setShowOnboarding] = useLocalStorage(
    "swissBudgetPro_showOnboarding",
    true
  );
  const [userProgress, setUserProgress] = useLocalStorage(
    "swissBudgetPro_userProgress",
    {
      hasBasicInfo: false,
      hasIncomeData: false,
      hasExpenseData: false,
      hasSavingsGoals: false,
      hasSwissConfig: false,
      completionPercentage: 0,
      onboardingCompleted: false,
    }
  );

  const sumOfFixedSavingsGoals = savings.reduce(
    (sum, s) => sum + (parseFloat(s.amount) || 0),
    0
  );
  let currentCompanyIncomeMonthly = 0;
  if (currentAge >= companyIncomeStartAge) {
    currentCompanyIncomeMonthly = parseFloat(companyIncome) || 0;
  }
  const currentHsluIncomeMonthly = hsluIncomeAmount;
  const currentRuagIncomeMonthly = ruagIncomeAmount;
  const totalMonthlyIncome =
    currentPrimaryIncome +
    currentCompanyIncomeMonthly +
    currentHsluIncomeMonthly +
    currentRuagIncomeMonthly;
  const totalExpenses = expenses.reduce(
    (sum, expense) => sum + (parseFloat(expense.amount) || 0),
    0
  );
  const emergencyFundTarget = totalExpenses * emergencyFundTargetMonths;
  const emergencyFundCurrent = parseFloat(currentSavings) || 0; // Assuming current savings includes emergency fund
  const emergencyFundReached = emergencyFundCurrent >= emergencyFundTarget;

  // Find emergency fund and investment portfolio in savings
  const emergencyFundGoal = savings.find((s) =>
    s.goal.toLowerCase().includes("emergency")
  );
  const investmentPortfolioGoal = savings.find(
    (s) =>
      s.goal.toLowerCase().includes("investment") ||
      s.goal.toLowerCase().includes("portfolio")
  );
  const emergencyFundMonthly = parseFloat(emergencyFundGoal?.amount || 0);

  // Calculate effective savings (accounting for emergency fund redirection)
  const calculateEffectiveSavings = () => {
    let effectiveTotal = 0;
    savings.forEach((sav) => {
      const amount = parseFloat(sav.amount) || 0;
      const isEmergencyFund = sav.goal.toLowerCase().includes("emergency");
      const isInvestmentPortfolio =
        sav.goal.toLowerCase().includes("investment") ||
        sav.goal.toLowerCase().includes("portfolio");

      if (isEmergencyFund && emergencyFundReached) {
        // Don't count emergency fund if target is reached
        effectiveTotal += 0;
      } else if (isInvestmentPortfolio && emergencyFundReached) {
        // Add emergency fund contribution to investment portfolio
        effectiveTotal += amount + emergencyFundMonthly;
      } else {
        effectiveTotal += amount;
      }
    });
    return effectiveTotal;
  };

  const effectiveTotalSavings = calculateEffectiveSavings();
  const totalSavingsDisplay = effectiveTotalSavings;
  const essentialExpenses = expenses
    .filter((e) => e.essential)
    .reduce((sum, expense) => sum + (parseFloat(expense.amount) || 0), 0);
  const nonEssentialExpenses = totalExpenses - essentialExpenses;
  const remaining = totalMonthlyIncome - totalExpenses - totalSavingsDisplay;
  const savingsRate =
    totalMonthlyIncome > 0
      ? (totalSavingsDisplay / totalMonthlyIncome) * 100
      : 0;

  // Onboarding and Progress Helper Functions
  // (updateUserProgress logic is now inline in useEffect below)

  const handleOnboardingComplete = useCallback(
    (data) => {
      // Update state with onboarding data
      if (data.basicInfo) {
        setCurrentAge(data.basicInfo.age);
        setRetirementAge(data.basicInfo.retirementAge);
        setTargetRetirementIncome(data.basicInfo.targetIncome);
      }
      if (data.income) {
        setMonthlyIncome(data.income.primaryIncome);
      }
      if (data.expenses && data.expenses.length > 0) {
        setExpenses(data.expenses);
      }
      if (data.savings && data.savings.length > 0) {
        setSavings(data.savings);
      }
      if (data.swissConfig) {
        setSelectedCanton(data.swissConfig.canton);
      }

      setShowOnboarding(false);
      setActiveTab("dashboard");
    },
    [
      setCurrentAge,
      setRetirementAge,
      setTargetRetirementIncome,
      setMonthlyIncome,
      setExpenses,
      setSavings,
      setSelectedCanton,
      setShowOnboarding,
      setActiveTab,
    ]
  );

  // Update progress whenever relevant data changes
  useEffect(() => {
    const hasBasicInfo = currentAge > 0 && retirementAge > 0;
    const hasIncomeData = parseFloat(monthlyIncome) > 0;
    const hasExpenseData = expenses.length > 0 && totalExpenses > 0;
    const hasSavingsGoals = savings.length > 0 && totalSavingsDisplay > 0;
    const hasSwissConfig = selectedCanton !== "";

    const completedSteps = [
      hasBasicInfo,
      hasIncomeData,
      hasExpenseData,
      hasSavingsGoals,
      hasSwissConfig,
    ].filter(Boolean).length;

    const completionPercentage = (completedSteps / 5) * 100;
    const onboardingCompleted = completionPercentage === 100;

    const newProgress = {
      hasBasicInfo,
      hasIncomeData,
      hasExpenseData,
      hasSavingsGoals,
      hasSwissConfig,
      completionPercentage,
      onboardingCompleted,
    };

    setUserProgress(newProgress);

    // Auto-hide onboarding when completed
    if (onboardingCompleted && showOnboarding) {
      setShowOnboarding(false);
    }
  }, [
    currentAge,
    retirementAge,
    monthlyIncome,
    expenses.length,
    totalExpenses,
    savings.length,
    totalSavingsDisplay,
    selectedCanton,
    showOnboarding,
  ]);

  const calculateRequiredSavings = () => {
    const targetInc = parseFloat(targetRetirementIncome) || 0;
    const yearsToRet = Math.max(0, retirementAge - currentAge);
    const currentSavAmount = parseFloat(currentSavings) || 0;
    const annualRet = Math.max(0, effectiveExpectedReturn / 100);
    const infl = Math.max(0, effectiveInflationRate / 100);

    if (yearsToRet <= 0 || targetInc <= 0)
      return {
        requiredMonthlySavings: 0,
        gap: 0,
        feasible: false,
        recommendations: [],
        requiredPortfolio: 0,
        yearsToRetirement: 0,
        projectedIncome: 0,
      };

    const reqPortfolio = (targetInc * 12) / 0.04;
    const infFactor = Math.pow(1 + infl, yearsToRet);
    const futReqPortfolio = reqPortfolio * infFactor;
    const futCurrSav = currentSavAmount * Math.pow(1 + annualRet, yearsToRet);
    const addNeeded = Math.max(0, futReqPortfolio - futCurrSav);
    const monthlyRetRate = annualRet / 12;
    const numMonths = yearsToRet * 12;
    let reqMonthlySav = 0;
    if (addNeeded > 0 && numMonths > 0) {
      reqMonthlySav =
        monthlyRetRate > 0
          ? (addNeeded * monthlyRetRate) /
            (Math.pow(1 + monthlyRetRate, numMonths) - 1)
          : addNeeded / numMonths;
    }
    const gap = reqMonthlySav - effectiveTotalSavings;
    const feasible = gap <= remaining;

    const recommendations = [];
    if (gap > 0) {
      recommendations.push({
        type: "warning",
        icon: "⚠️",
        title: "Savings Gap Detected",
        description: `To meet your target, you need to save an additional ${formatCurrency(
          gap
        )} per month based on current income structure.`,
        action: "Increase savings, reduce expenses, or adjust target.",
      });
    } else {
      recommendations.push({
        type: "success",
        icon: "🎉",
        title: "On Track (Current View)",
        description: `Your current savings plan covers the required amount by ${formatCurrency(
          Math.abs(gap)
        )}. The projection chart will show the full picture.`,
        action: "Review projection chart for dynamic income effects.",
      });
    }
    if (savingsRate < targetOverallSavingsRate && totalMonthlyIncome > 0) {
      recommendations.push({
        type: "info",
        icon: "📈",
        title: "Boost Savings Rate",
        description: `Current rate: ${savingsRate.toFixed(
          1
        )}%. Your target is ${targetOverallSavingsRate.toFixed(
          1
        )}%. Consider increasing contributions.`,
        action: `Aim for ${formatCurrency(
          totalMonthlyIncome * (targetOverallSavingsRate / 100)
        )} monthly savings.`,
      });
    }
    const pillar3aCont =
      savings.find((s) => s.goal.toLowerCase().includes("pillar 3a"))?.amount ||
      0;
    if (parseFloat(pillar3aCont) * 12 < pillar3aLimit) {
      recommendations.push({
        type: "info",
        icon: "🇨🇭",
        title: "Maximize Pillar 3a",
        description: `Current: ${formatCurrency(
          parseFloat(pillar3aCont) * 12
        )}/year. Limit: ${formatCurrency(pillar3aLimit)}/year.`,
        action: `Increase by ${formatCurrency(
          (pillar3aLimit - parseFloat(pillar3aCont) * 12) / 12
        )} monthly for tax benefits.`,
      });
    }
    return {
      requiredMonthlySavings: reqMonthlySav,
      currentSavings: effectiveTotalSavings,
      gap,
      feasible,
      recommendations,
      requiredPortfolio: reqPortfolio,
      yearsToRetirement: yearsToRet,
      projectedIncome: (futCurrSav * 0.04) / 12 / infFactor,
    };
  };

  const calculateDetailedTrajectory = () => {
    const yearsToRetirement = Math.max(0, retirementAge - currentAge);
    const currentSavingsAmount = parseFloat(currentSavings) || 0;
    const currentPensionAmount = parseFloat(currentPensionLeavingBenefits) || 0;
    const annualReturnRate = Math.max(0, effectiveExpectedReturn / 100);
    const inflation = Math.max(0, effectiveInflationRate / 100);
    const companyGrowth = Math.max(0, companyIncomeGrowthRate / 100);

    let trajectory = [];
    let currentBalance = currentSavingsAmount;
    let currentPensionBalance = currentPensionAmount;
    let cumulativeContributions = currentSavingsAmount;
    let cumulativePensionContributions = currentPensionAmount;
    let currentAnnualCompanyIncomeValue = parseFloat(companyIncome) || 0;
    let emergencyFundBalance = 0; // Track emergency fund separately

    // Find pillar 3a monthly contribution
    const pillar3aGoal = savings.find((s) =>
      s.goal.toLowerCase().includes("pillar 3a")
    );
    const pillar3aMonthlyContribution = parseFloat(pillar3aGoal?.amount || 0);

    for (let year = 0; year <= yearsToRetirement; year++) {
      const age = parseInt(currentAge) + year;

      let yearPrimaryIncome = age < retirementAge ? currentPrimaryIncome : 0;
      let yearHsluIncome = age < retirementAge ? hsluIncomeAmount : 0;
      let yearRuagIncome = age < retirementAge ? ruagIncomeAmount : 0;
      let yearCompanyIncome = 0;

      if (age >= companyIncomeStartAge) {
        if (age === companyIncomeStartAge) {
          yearCompanyIncome = parseFloat(companyIncome) || 0;
          currentAnnualCompanyIncomeValue = yearCompanyIncome;
        } else {
          currentAnnualCompanyIncomeValue *= 1 + companyGrowth;
          yearCompanyIncome = currentAnnualCompanyIncomeValue;
        }
      }

      if (age >= retirementAge) {
        yearPrimaryIncome = 0;
        yearHsluIncome = 0;
        yearRuagIncome = 0;
      }

      const currentYearTotalMonthlyIncome =
        yearPrimaryIncome + yearCompanyIncome + yearHsluIncome + yearRuagIncome;

      // Calculate emergency fund status for this year
      const yearlyExpenses = totalExpenses * 12; // Assuming expenses stay constant
      const emergencyFundTargetAmount =
        totalExpenses * emergencyFundTargetMonths;
      const emergencyFundReachedThisYear =
        emergencyFundBalance >= emergencyFundTargetAmount;

      let currentYearAnnualSavings;
      let currentYearPillar3aContributions = 0;
      let currentYearPensionContributions = 0;

      // Calculate pension contributions (only if working)
      if (age < retirementAge) {
        currentYearPensionContributions = adjustedPensionContribution * 12;
      }

      if (age >= companyIncomeStartAge && currentYearTotalMonthlyIncome > 0) {
        const totalSavingsRate =
          currentYearTotalMonthlyIncome * (targetOverallSavingsRate / 100) * 12;
        currentYearPillar3aContributions = Math.min(
          pillar3aMonthlyContribution * 12,
          pillar3aLimit
        );
        currentYearAnnualSavings =
          totalSavingsRate - currentYearPillar3aContributions;
      } else {
        // Calculate effective savings for this year
        let yearEffectiveSavings = 0;
        savings.forEach((sav) => {
          const monthlyAmount = parseFloat(sav.amount) || 0;
          const isEmergencyFund = sav.goal.toLowerCase().includes("emergency");
          const isInvestmentPortfolio =
            sav.goal.toLowerCase().includes("investment") ||
            sav.goal.toLowerCase().includes("portfolio");
          const isPillar3a = sav.goal.toLowerCase().includes("pillar 3a");

          if (isPillar3a) {
            currentYearPillar3aContributions += Math.min(
              monthlyAmount * 12,
              pillar3aLimit
            );
          } else if (isEmergencyFund && emergencyFundReachedThisYear) {
            // Don't contribute to emergency fund if target reached
            yearEffectiveSavings += 0;
          } else if (isInvestmentPortfolio && emergencyFundReachedThisYear) {
            // Add redirected emergency fund to investment
            yearEffectiveSavings += monthlyAmount + emergencyFundMonthly;
          } else {
            yearEffectiveSavings += monthlyAmount;
          }
        });
        currentYearAnnualSavings = yearEffectiveSavings * 12;
      }
      currentYearAnnualSavings = Math.min(
        currentYearAnnualSavings,
        Math.max(
          0,
          currentYearTotalMonthlyIncome * 12 - currentYearPillar3aContributions
        )
      );

      // Update emergency fund balance
      if (!emergencyFundReachedThisYear) {
        emergencyFundBalance += emergencyFundMonthly * 12;
      }

      const inflationFactor = Math.pow(1 + inflation, year);
      const totalBalance = currentBalance + currentPensionBalance;
      const realBalance = totalBalance / inflationFactor;
      const fireNumberTarget =
        (currentPrimaryIncome +
          hsluIncomeAmount +
          ruagIncomeAmount +
          ((currentAge >= companyIncomeStartAge
            ? parseFloat(companyIncome)
            : 0) || 0)) *
        12 *
        25;
      const fireProgress =
        fireNumberTarget > 0 ? (realBalance / fireNumberTarget) * 100 : 0;

      trajectory.push({
        year,
        age,
        balance: Math.max(0, currentBalance),
        pensionBalance: Math.max(0, currentPensionBalance),
        pillar3aBalance: Math.max(0, currentPensionBalance), // Keep for compatibility
        totalBalance: Math.max(0, totalBalance),
        realBalance: Math.max(0, realBalance),
        totalContributions:
          cumulativeContributions + cumulativePensionContributions,
        interestEarned: Math.max(
          0,
          totalBalance -
            (cumulativeContributions + cumulativePensionContributions)
        ),
        monthlyIncome: Math.max(0, (realBalance * 0.04) / 12),
        fireProgress: Math.min(100, Math.max(0, fireProgress)),
        emergencyFundBalance: emergencyFundBalance,
        emergencyFundComplete: emergencyFundReachedThisYear,
      });

      if (year < yearsToRetirement) {
        currentBalance =
          (currentBalance + currentYearAnnualSavings) * (1 + annualReturnRate);
        currentPensionBalance =
          (currentPensionBalance + currentYearPensionContributions) *
          (1 + annualReturnRate);
        cumulativeContributions += currentYearAnnualSavings;
        cumulativePensionContributions += currentYearPensionContributions;
      } else if (age >= retirementAge) {
        currentBalance *= 1 + annualReturnRate;
        currentPensionBalance *= 1 + annualReturnRate;
      }
    }
    return trajectory;
  };

  const calculateCompanyIncomeProgression = () => {
    const yearsToRetirement = Math.max(0, retirementAge - currentAge);
    const companyGrowth = Math.max(0, companyIncomeGrowthRate / 100);
    const baseMonthlyIncome = parseFloat(companyIncome) || 0;

    if (baseMonthlyIncome === 0) return [];

    let progression = [];
    let currentAnnualIncome = baseMonthlyIncome;
    let cumulativeEarnings = 0;

    for (let year = 0; year <= yearsToRetirement; year++) {
      const age = parseInt(currentAge) + year;
      const currentYear = 2025 + year;

      let yearlyIncome = 0;
      if (age >= companyIncomeStartAge && age < retirementAge) {
        if (age === companyIncomeStartAge) {
          currentAnnualIncome = baseMonthlyIncome;
        } else {
          currentAnnualIncome *= 1 + companyGrowth;
        }
        yearlyIncome = currentAnnualIncome * 12;
        cumulativeEarnings += yearlyIncome;
      }

      progression.push({
        year: currentYear,
        age,
        monthlyIncome:
          age >= companyIncomeStartAge && age < retirementAge
            ? currentAnnualIncome
            : 0,
        yearlyIncome,
        cumulativeEarnings,
        isActive: age >= companyIncomeStartAge && age < retirementAge,
      });
    }

    return progression;
  };

  const companyIncomeProgression = calculateCompanyIncomeProgression();
  const totalCompanyEarnings =
    companyIncomeProgression.length > 0
      ? companyIncomeProgression[companyIncomeProgression.length - 1]
          .cumulativeEarnings
      : 0;

  // Swiss Economic Data Engine (moved up to avoid circular dependency)
  const SwissEconomicDataEngine = {
    // Swiss National Bank (SNB) Data
    snbData: {
      policyRate: 1.25, // Current SNB policy rate (%)
      inflationTarget: 2.0, // SNB inflation target (%)
      currentInflation: 1.7, // Current Swiss CPI inflation (%)
      lastUpdate: new Date("2024-12-15"),
      nextMeeting: new Date("2025-03-20"),
      rateHistory: [
        { date: "2024-12-01", rate: 1.25, inflation: 1.7 },
        { date: "2024-09-01", rate: 1.25, inflation: 1.4 },
        { date: "2024-06-01", rate: 1.25, inflation: 1.3 },
        { date: "2024-03-01", rate: 1.75, inflation: 1.2 },
        { date: "2023-12-01", rate: 1.75, inflation: 1.7 },
        { date: "2023-09-01", rate: 1.75, inflation: 2.2 },
      ],
    },

    // SIX Swiss Exchange Market Data
    marketData: {
      smi: {
        value: 11847.2,
        change: 0.8,
        ytdReturn: 8.7,
        pe: 15.2,
        dividend: 3.1,
      },
      spi: {
        value: 15234.8,
        change: 0.6,
        ytdReturn: 9.2,
        pe: 14.8,
        dividend: 3.3,
      },
      bonds: {
        government10y: 0.68,
        corporate: 1.45,
        municipalAvg: 0.85,
      },
      lastUpdate: new Date("2024-12-15T16:30:00Z"),
    },

    // Currency Data
    currencyData: {
      chfEur: 0.9234,
      chfUsd: 0.8876,
      chfGbp: 1.1234,
      lastUpdate: new Date("2024-12-15T16:30:00Z"),
    },

    // Calculate dynamic returns based on current economic conditions
    calculateDynamicReturns() {
      const currentInflation = this.snbData.currentInflation;
      const policyRate = this.snbData.policyRate;
      const marketPE = this.marketData.smi.pe;
      const bondYield = this.marketData.bonds.government10y;

      // Base returns adjusted for current conditions
      const equityReturn = Math.max(
        3.0,
        Math.min(
          12.0,
          6.5 + (15.0 - marketPE) * 0.3 + (currentInflation - 2.0) * 0.5
        )
      );

      const bondReturn = Math.max(
        0.5,
        Math.min(6.0, bondYield + 0.8 + currentInflation * 0.3)
      );

      const conservativeReturn = Math.max(
        1.0,
        Math.min(4.0, policyRate + 0.5 + currentInflation * 0.2)
      );

      // Portfolio mixes
      const conservative = conservativeReturn * 0.8 + bondReturn * 0.2;
      const mixed = equityReturn * 0.6 + bondReturn * 0.4;
      const aggressive = equityReturn * 0.8 + bondReturn * 0.2;

      return {
        equity: equityReturn,
        bonds: bondReturn,
        conservative,
        mixed,
        aggressive,
        inflation: currentInflation,
        realReturn: mixed - currentInflation,
        confidence: this.assessConfidence(),
        lastUpdated: this.snbData.lastUpdate,
      };
    },

    // Assess confidence in projections
    assessConfidence() {
      const inflationStability = Math.abs(
        this.snbData.currentInflation - this.snbData.inflationTarget
      );
      const marketStability = this.marketData.smi.change;

      if (inflationStability < 0.5 && Math.abs(marketStability) < 1.0)
        return "high";
      if (inflationStability < 1.0 && Math.abs(marketStability) < 2.0)
        return "medium";
      return "low";
    },

    // Detect current economic regime
    detectEconomicRegime() {
      const inflation = this.snbData.currentInflation;
      const policyRate = this.snbData.policyRate;
      const marketReturn = this.marketData.smi.ytdReturn;

      if (inflation > 3.0 && policyRate > 2.0) {
        return {
          regime: "high_inflation",
          description:
            "High inflation environment - focus on real assets and inflation-protected investments",
          confidence: "high",
        };
      } else if (inflation < 1.0 && policyRate < 1.0) {
        return {
          regime: "low_growth",
          description:
            "Low growth, low inflation - consider longer investment horizons",
          confidence: "medium",
        };
      } else if (marketReturn > 10.0 && inflation < 2.5) {
        return {
          regime: "goldilocks",
          description:
            "Balanced growth environment - optimal for long-term wealth building",
          confidence: "high",
        };
      } else {
        return {
          regime: "transitional",
          description:
            "Economic transition period - maintain diversified approach",
          confidence: "medium",
        };
      }
    },

    // Generate economic alerts
    generateEconomicAlerts() {
      const alerts = [];
      const inflation = this.snbData.currentInflation;
      const policyRate = this.snbData.policyRate;

      if (inflation > this.snbData.inflationTarget + 0.5) {
        alerts.push({
          type: "warning",
          title: "Inflation Above Target",
          message: `Current inflation (${inflation}%) exceeds SNB target. Consider inflation-protected investments.`,
          action: "Review asset allocation for inflation protection",
        });
      }

      if (policyRate !== this.snbData.rateHistory[0]?.rate) {
        alerts.push({
          type: "info",
          title: "Interest Rate Change",
          message: `SNB policy rate changed to ${policyRate}%. This may affect bond and savings returns.`,
          action: "Reassess fixed-income allocations",
        });
      }

      return alerts;
    },

    // Update economic data (mock implementation)
    async updateEconomicData() {
      // In production, this would fetch from real APIs
      // For now, simulate small random changes
      const variation = () => (Math.random() - 0.5) * 0.1;

      this.snbData.currentInflation = Math.max(
        0,
        this.snbData.currentInflation + variation()
      );
      this.marketData.smi.value = Math.max(
        0,
        this.marketData.smi.value * (1 + variation() * 0.01)
      );
      this.snbData.lastUpdate = new Date();

      return {
        snb: this.snbData,
        market: this.marketData,
        currency: this.currencyData,
      };
    },
  };

  // Calculate dynamic returns first (needed for other calculations)
  const calculateDynamicReturns = useCallback(() => {
    if (!useDynamicReturns) {
      return {
        expectedReturn: parseFloat(expectedReturn) || 5.0,
        inflationRate: parseFloat(inflationRate) || 2.0,
        source: "manual",
      };
    }

    const dynamicReturns = SwissEconomicDataEngine.calculateDynamicReturns();

    return {
      expectedReturn: dynamicReturns.mixed,
      inflationRate: dynamicReturns.inflation,
      realReturn: dynamicReturns.realReturn,
      confidence: dynamicReturns.confidence,
      breakdown: {
        equity: dynamicReturns.equity,
        bonds: dynamicReturns.bonds,
        conservative: dynamicReturns.conservative,
        aggressive: dynamicReturns.aggressive,
      },
      source: "dynamic",
      lastUpdated: SwissEconomicDataEngine.snbData.lastUpdate,
    };
  }, [useDynamicReturns, expectedReturn, inflationRate]);

  const dynamicReturns = calculateDynamicReturns();

  // Use dynamic returns in calculations if enabled
  const effectiveExpectedReturn =
    useDynamicReturns && dynamicReturns.source === "dynamic"
      ? dynamicReturns.expectedReturn
      : parseFloat(expectedReturn) || 5.0;

  const effectiveInflationRate =
    useDynamicReturns && dynamicReturns.source === "dynamic"
      ? dynamicReturns.inflationRate
      : parseFloat(inflationRate) || 2.0;

  // Calculate FIRE number for Monte Carlo simulation
  const fireNumber = (parseFloat(targetRetirementIncome) || 0) * 12 * 25;

  const targetAnalysis = calculateRequiredSavings();
  const trajectory = calculateDetailedTrajectory();
  const finalProjection =
    trajectory.length > 0
      ? trajectory[trajectory.length - 1]
      : {
          fireProgress: 0,
          totalBalance: 0,
          pensionBalance: 0,
          balance: 0,
          monthlyIncome: 0,
          totalContributions: 0,
          interestEarned: 0,
        };

  // Swiss Tax Optimization Calculations with enhanced error handling
  const calculateSwissTaxOptimization = () => {
    try {
      // Validate inputs
      const annualIncome = Math.max(0, totalMonthlyIncome * 12);
      const netWorth = Math.max(
        0,
        (parseFloat(currentSavings) || 0) +
          (parseFloat(currentPensionLeavingBenefits) || 0) +
          (parseFloat(currentPillar3a) || 0)
      );

      // Validate required fields - handle both string and number types
      const currentAgeValue =
        typeof currentAge === "string" ? parseInt(currentAge) : currentAge;
      const retirementAgeValue =
        typeof retirementAge === "string"
          ? parseInt(retirementAge)
          : retirementAge;

      if (
        !selectedCanton ||
        !civilStatus ||
        !currentAgeValue ||
        !retirementAgeValue ||
        isNaN(currentAgeValue) ||
        isNaN(retirementAgeValue)
      ) {
        console.log("Tax calculation validation failed:", {
          selectedCanton,
          civilStatus,
          currentAge,
          retirementAge,
          currentAgeValue,
          retirementAgeValue,
          isNaN_currentAge: isNaN(currentAgeValue),
          isNaN_retirementAge: isNaN(retirementAgeValue),
        });
        throw new Error("Missing required tax calculation parameters");
      }

      const userProfile = {
        income: annualIncome,
        netWorth: netWorth,
        canton: selectedCanton,
        civilStatus: civilStatus,
        currentAge: currentAgeValue,
        retirementAge: retirementAgeValue,
        hasSecondPillar: hasSecondPillar,
      };

      // Validate age ranges
      if (userProfile.currentAge < 18 || userProfile.currentAge > 100) {
        throw new Error("Invalid current age");
      }
      if (userProfile.retirementAge <= userProfile.currentAge) {
        throw new Error("Retirement age must be greater than current age");
      }

      // Current tax situation with error handling
      const currentTaxSituation = SwissTaxEngine.calculateTotalTax(
        annualIncome,
        netWorth,
        selectedCanton,
        civilStatus
      );

      // Pillar 3a optimization with error handling
      const pillar3aOptimization = SwissTaxEngine.optimizePillar3a(userProfile);

      // Cantonal comparison with error handling
      const cantonalComparison = SwissTaxEngine.findOptimalCanton(userProfile);

      // Tax optimization recommendations with error handling
      const taxOptimizations =
        SwissTaxEngine.generateTaxOptimizations(userProfile);

      return {
        currentTaxSituation,
        pillar3aOptimization,
        cantonalComparison,
        taxOptimizations,
        userProfile,
      };
    } catch (error) {
      console.error("Error in calculateSwissTaxOptimization:", error);
      console.log("Debug values at error:", {
        selectedCanton,
        civilStatus,
        currentAge,
        retirementAge,
        totalMonthlyIncome,
        currentSavings,
        currentPensionLeavingBenefits,
        currentPillar3a,
      });
      // Return safe default values
      return {
        currentTaxSituation: {
          totalTax: 0,
          effectiveRate: 0,
          marginalRate: 0,
          federalTax: 0,
          cantonalTax: 0,
          wealthTax: 0,
        },
        pillar3aOptimization: {
          currentContribution: 0,
          optimalContribution: 0,
          taxSavings: 0,
          recommendations: [],
        },
        cantonalComparison: [],
        taxOptimizations: [],
        userProfile: {
          income: 0,
          netWorth: 0,
          canton: selectedCanton || "ZH",
          civilStatus: civilStatus || "single",
          currentAge:
            (typeof currentAge === "string"
              ? parseInt(currentAge)
              : currentAge) || 30,
          retirementAge:
            (typeof retirementAge === "string"
              ? parseInt(retirementAge)
              : retirementAge) || 65,
          hasSecondPillar: hasSecondPillar || false,
        },
      };
    }
  };

  // Add error handling for tax calculations
  const swissTaxAnalysis = useMemo(() => {
    // Only run calculation if all required values are available
    if (!selectedCanton || !civilStatus || !currentAge || !retirementAge) {
      return {
        currentTaxSituation: { totalTax: 0, effectiveRate: 0, marginalRate: 0 },
        pillar3aOptimization: {
          currentContribution: 0,
          optimalContribution: 0,
          taxSavings: 0,
        },
        cantonalComparison: [],
        taxOptimizations: [],
        userProfile: {
          income: 0,
          netWorth: 0,
          canton: selectedCanton || "ZH",
          civilStatus: civilStatus || "single",
          currentAge:
            (typeof currentAge === "string"
              ? parseInt(currentAge)
              : currentAge) || 30,
          retirementAge:
            (typeof retirementAge === "string"
              ? parseInt(retirementAge)
              : retirementAge) || 65,
          hasSecondPillar: hasSecondPillar || false,
        },
      };
    }

    try {
      return calculateSwissTaxOptimization();
    } catch (error) {
      console.error("Error in Swiss tax calculation:", error);
      return {
        currentTaxSituation: { totalTax: 0, effectiveRate: 0, marginalRate: 0 },
        pillar3aOptimization: {
          currentContribution: 0,
          optimalContribution: 0,
          taxSavings: 0,
        },
        cantonalComparison: [],
        taxOptimizations: [],
        userProfile: {
          income: 0,
          netWorth: 0,
          canton: selectedCanton || "ZH",
          civilStatus: civilStatus || "single",
          currentAge:
            (typeof currentAge === "string"
              ? parseInt(currentAge)
              : currentAge) || 30,
          retirementAge:
            (typeof retirementAge === "string"
              ? parseInt(retirementAge)
              : retirementAge) || 65,
          hasSecondPillar: hasSecondPillar || false,
        },
      };
    }
  }, [
    totalMonthlyIncome,
    currentSavings,
    currentPensionLeavingBenefits,
    currentPillar3a,
    selectedCanton,
    civilStatus,
    currentAge,
    retirementAge,
    hasSecondPillar,
  ]);

  // Monte Carlo Simulation Integration with enhanced error handling
  const runMonteCarloSimulation = useCallback(async () => {
    setSimulationRunning(true);

    try {
      // Validate inputs before running simulation
      const currentAgeNum = parseInt(currentAge);
      const retirementAgeNum = parseInt(retirementAge);
      const currentSavingsNum = parseFloat(currentSavings) || 0;

      if (
        isNaN(currentAgeNum) ||
        isNaN(retirementAgeNum) ||
        currentAgeNum >= retirementAgeNum
      ) {
        throw new Error("Invalid age parameters for simulation");
      }

      if (currentSavingsNum < 0 || totalSavingsDisplay < 0 || fireNumber <= 0) {
        throw new Error("Invalid financial parameters for simulation");
      }

      const scenarios = MonteCarloEngine.generateStressTestScenarios();
      if (!scenarios || scenarios.length === 0) {
        throw new Error("No stress test scenarios available");
      }

      const selectedScenario =
        scenarios[Math.min(stressTestScenario, scenarios.length - 1)];
      if (!selectedScenario) {
        throw new Error("Invalid stress test scenario selected");
      }

      const simulationParams = {
        currentAge: currentAgeNum,
        retirementAge: retirementAgeNum,
        currentSavings: Math.max(0, currentSavingsNum),
        monthlyContribution: Math.max(0, totalSavingsDisplay),
        targetAmount: Math.max(1000, fireNumber), // Minimum target of CHF 1,000
        expectedReturn: Math.max(0.01, Math.min(0.15, effectiveExpectedReturn)), // Clamp between 1% and 15%
        volatility: Math.max(
          0.01,
          Math.min(0.5, selectedScenario.volatility || 0.15)
        ),
        inflationRate: Math.max(0, Math.min(0.1, effectiveInflationRate)), // Clamp between 0% and 10%
        inflationVolatility: Math.max(
          0.01,
          Math.min(0.1, selectedScenario.inflationVolatility || 0.02)
        ),
        economicShocks: selectedScenario.economicShocks || [],
      };

      // Validate simulation parameters
      if (simulationParams.expectedReturn <= simulationParams.inflationRate) {
        console.warn(
          "Expected return is not significantly higher than inflation rate"
        );
      }

      // Run simulation in chunks to avoid blocking UI
      const results = await new Promise((resolve, reject) => {
        setTimeout(() => {
          try {
            const simulationResults = MonteCarloEngine.runSimulation(
              simulationParams,
              Math.min(simulationIterations, 10000)
            ); // Cap iterations
            if (
              !simulationResults ||
              typeof simulationResults.successRate === "undefined"
            ) {
              throw new Error("Invalid simulation results");
            }
            resolve(simulationResults);
          } catch (simError) {
            reject(simError);
          }
        }, 100);
      });

      // Calculate safe withdrawal rate with error handling
      let withdrawalAnalysis;
      try {
        withdrawalAnalysis = MonteCarloEngine.calculateSafeWithdrawalRate(
          results.averageBalance || 0
        );
      } catch (withdrawalError) {
        console.error("Error calculating withdrawal rate:", withdrawalError);
        withdrawalAnalysis = {
          safeWithdrawalRate: 3.5, // Conservative default
          confidence: 0.8,
          recommendation:
            "Use conservative 3.5% withdrawal rate due to calculation error",
        };
      }

      setMonteCarloResults({
        ...results,
        withdrawalAnalysis,
        scenario: selectedScenario,
        parameters: simulationParams,
      });
    } catch (error) {
      console.error("Monte Carlo simulation error:", error);
      // Set error state or default results
      setMonteCarloResults({
        successRate: 0,
        averageBalance: 0,
        percentiles: { p10: 0, p25: 0, p50: 0, p75: 0, p90: 0 },
        riskMetrics: { probabilityOfRuin: 100, maxDrawdown: 0 },
        withdrawalAnalysis: {
          safeWithdrawalRate: 3.5,
          confidence: 0.5,
          recommendation: "Simulation failed - using conservative estimates",
        },
        scenario: { name: "Error", volatility: 0.15 },
        parameters: {},
        error: error.message,
      });
    } finally {
      setSimulationRunning(false);
    }
  }, [
    stressTestScenario,
    simulationIterations,
    currentAge,
    retirementAge,
    currentSavings,
    totalSavingsDisplay,
    fireNumber,
    effectiveExpectedReturn,
    effectiveInflationRate,
  ]);

  // Calculate advanced analytics
  const calculateAdvancedAnalytics = useCallback(() => {
    if (!monteCarloResults) return null;

    const { successRate, percentiles, riskMetrics, withdrawalAnalysis } =
      monteCarloResults;

    return {
      riskAssessment: {
        level: successRate > 90 ? "Low" : successRate > 75 ? "Medium" : "High",
        successProbability: successRate,
        worstCaseScenario: percentiles.p10,
        bestCaseScenario: percentiles.p90,
        medianOutcome: percentiles.p50,
      },
      withdrawalStrategy: {
        safeRate: withdrawalAnalysis.safeWithdrawalRate,
        confidence: withdrawalAnalysis.confidence,
        annualIncome:
          (monteCarloResults.averageBalance *
            withdrawalAnalysis.safeWithdrawalRate) /
          100,
        monthlyIncome:
          (monteCarloResults.averageBalance *
            withdrawalAnalysis.safeWithdrawalRate) /
          100 /
          12,
      },
      optimizationSuggestions: generateOptimizationSuggestions(
        successRate,
        riskMetrics
      ),
    };
  }, [monteCarloResults]);

  const generateOptimizationSuggestions = (successRate, riskMetrics) => {
    const suggestions = [];

    if (successRate < 80) {
      suggestions.push({
        type: "increase_savings",
        priority: "high",
        title: "Increase Monthly Savings",
        description: `Your success rate is ${successRate.toFixed(
          1
        )}%. Consider increasing monthly savings by 20-30%.`,
        impact: "Could improve success rate to 85-90%",
      });
    }

    if (successRate < 70) {
      suggestions.push({
        type: "delay_retirement",
        priority: "high",
        title: "Consider Delaying Retirement",
        description:
          "Working 2-3 additional years could significantly improve your financial security.",
        impact: "Could improve success rate to 90%+",
      });
    }

    if (riskMetrics.probabilityOfRuin > 15) {
      suggestions.push({
        type: "reduce_risk",
        priority: "medium",
        title: "Reduce Portfolio Risk",
        description:
          "Consider a more conservative asset allocation to reduce downside risk.",
        impact: "Lower volatility, more predictable outcomes",
      });
    }

    return suggestions;
  };

  const advancedAnalytics = calculateAdvancedAnalytics();

  // Economic Data Integration with enhanced error handling
  const fetchEconomicData = useCallback(async () => {
    try {
      // Check if SwissEconomicDataEngine is available
      if (typeof SwissEconomicDataEngine === "undefined") {
        console.warn(
          "SwissEconomicDataEngine not available, using fallback data"
        );
        setEconomicAlerts([]);
        return null;
      }

      const data = await SwissEconomicDataEngine.updateEconomicData();

      // Generate alerts based on current data with error handling
      try {
        const alerts = SwissEconomicDataEngine.generateEconomicAlerts();
        setEconomicAlerts(Array.isArray(alerts) ? alerts : []);
      } catch (alertError) {
        console.error("Error generating economic alerts:", alertError);
        setEconomicAlerts([]);
      }

      return data;
    } catch (error) {
      console.error("Failed to fetch economic data:", error);
      // Set empty alerts on error
      setEconomicAlerts([]);
      return null;
    }
  }, [setEconomicAlerts]);

  // Dynamic returns are now calculated earlier in the component

  // Economic data fetching effect
  useEffect(() => {
    fetchEconomicData();

    // Set up periodic updates (every 4 hours)
    const interval = setInterval(fetchEconomicData, 4 * 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, [fetchEconomicData]);

  // Data persistence functionality with error handling
  const getCurrentStateForSave = () => {
    try {
      return {
        // Core financial data
        monthlyIncome: monthlyIncome || "0",
        incomePercentage: incomePercentage || 100,
        companyIncome: companyIncome || "0",
        companyIncomeStartYear: companyIncomeStartYear || 2025,
        companyIncomeGrowthRate: companyIncomeGrowthRate || 0,
        hsluIncome: hsluIncome || "0",
        ruagIncome: ruagIncome || "0",
        expenses: Array.isArray(expenses) ? expenses : [],
        savings: Array.isArray(savings) ? savings : [],
        emergencyFundTargetMonths: emergencyFundTargetMonths || 6,
        currentAge: currentAge || "30",
        retirementAge: retirementAge || "65",
        currentSavings: currentSavings || "0",
        expectedReturn: expectedReturn || "7",
        inflationRate: inflationRate || "2",
        targetRetirementIncome: targetRetirementIncome || "0",
        currentPensionLeavingBenefits: currentPensionLeavingBenefits || "0",
        targetOverallSavingsRate: targetOverallSavingsRate || 20,

        // Swiss Tax Optimization data
        selectedCanton: selectedCanton || "ZH",
        civilStatus: civilStatus || "single",
        hasSecondPillar: hasSecondPillar || false,
        currentPillar3a: currentPillar3a || "0",

        // Calculated values for snapshots
        totalMonthlyIncome: totalMonthlyIncome || 0,
        totalExpenses: totalExpenses || 0,
        totalSavingsDisplay: totalSavingsDisplay || 0,
        savingsRate: savingsRate || 0,
        fireProgress: finalProjection?.fireProgress || 0,

        // UI state
        darkMode: darkMode || false,
        activeTab: activeTab || "overview",

        // Metadata
        timestamp: new Date().toISOString(),
        version: "1.0",
      };
    } catch (error) {
      console.error("Error creating save state:", error);
      // Return minimal safe state
      return {
        monthlyIncome: "0",
        currentAge: "30",
        retirementAge: "65",
        selectedCanton: "ZH",
        civilStatus: "single",
        timestamp: new Date().toISOString(),
        version: "1.0",
      };
    }
  };

  const loadStateFromSave = (savedData) => {
    if (!savedData) return;

    // Core financial data
    if (savedData.monthlyIncome !== undefined)
      setMonthlyIncome(savedData.monthlyIncome);
    if (savedData.incomePercentage !== undefined)
      setIncomePercentage(savedData.incomePercentage);
    if (savedData.companyIncome !== undefined)
      setCompanyIncome(savedData.companyIncome);
    if (savedData.companyIncomeStartYear !== undefined)
      setCompanyIncomeStartYear(savedData.companyIncomeStartYear);
    if (savedData.companyIncomeGrowthRate !== undefined)
      setCompanyIncomeGrowthRate(savedData.companyIncomeGrowthRate);
    if (savedData.hsluIncome !== undefined) setHsluIncome(savedData.hsluIncome);
    if (savedData.ruagIncome !== undefined) setRuagIncome(savedData.ruagIncome);
    if (savedData.expenses !== undefined) setExpenses(savedData.expenses);
    if (savedData.savings !== undefined) setSavings(savedData.savings);
    if (savedData.emergencyFundTargetMonths !== undefined)
      setEmergencyFundTargetMonths(savedData.emergencyFundTargetMonths);
    if (savedData.currentAge !== undefined) setCurrentAge(savedData.currentAge);
    if (savedData.retirementAge !== undefined)
      setRetirementAge(savedData.retirementAge);
    if (savedData.currentSavings !== undefined)
      setCurrentSavings(savedData.currentSavings);
    if (savedData.expectedReturn !== undefined)
      setExpectedReturn(savedData.expectedReturn);
    if (savedData.inflationRate !== undefined)
      setInflationRate(savedData.inflationRate);
    if (savedData.targetRetirementIncome !== undefined)
      setTargetRetirementIncome(savedData.targetRetirementIncome);
    if (savedData.currentPensionLeavingBenefits !== undefined)
      setCurrentPensionLeavingBenefits(savedData.currentPensionLeavingBenefits);
    if (savedData.targetOverallSavingsRate !== undefined)
      setTargetOverallSavingsRate(savedData.targetOverallSavingsRate);

    // Swiss Tax Optimization data
    if (savedData.selectedCanton !== undefined)
      setSelectedCanton(savedData.selectedCanton);
    if (savedData.civilStatus !== undefined)
      setCivilStatus(savedData.civilStatus);
    if (savedData.hasSecondPillar !== undefined)
      setHasSecondPillar(savedData.hasSecondPillar);
    if (savedData.currentPillar3a !== undefined)
      setCurrentPillar3a(savedData.currentPillar3a);

    // UI state
    if (savedData.darkMode !== undefined) setDarkMode(savedData.darkMode);
    if (savedData.activeTab !== undefined) setActiveTab(savedData.activeTab);
  };

  // Auto-save effect
  useEffect(() => {
    if (!autoSaveEnabled) return;

    const autoSaveInterval = setInterval(() => {
      const currentState = getCurrentStateForSave();
      const success = DataService.autoSave(currentState, currentPlanName);
      if (success) {
        setLastSaved(new Date().toISOString());
      }
    }, 30000); // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval);
  }, [
    autoSaveEnabled,
    currentPlanName,
    monthlyIncome,
    expenses,
    savings,
    currentAge,
    retirementAge,
    selectedCanton,
    civilStatus,
    hasSecondPillar,
    currentPillar3a,
  ]); // Key dependencies

  // Monthly snapshot effect - save snapshots when significant data changes
  useEffect(() => {
    const lastSnapshotKey = `swissBudgetPro_lastSnapshot_${currentPlanName}`;
    const lastSnapshotTime = localStorage.getItem(lastSnapshotKey);
    const now = new Date();
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Save snapshot if no previous snapshot or if it's been more than a month
    if (!lastSnapshotTime || new Date(lastSnapshotTime) < oneMonthAgo) {
      const currentState = getCurrentStateForSave();
      const success = DataService.saveSnapshot(currentState, currentPlanName);
      if (success) {
        localStorage.setItem(lastSnapshotKey, now.toISOString());
      }
    }
  }, [
    currentPlanName,
    totalMonthlyIncome,
    totalExpenses,
    totalSavingsDisplay,
    savingsRate,
  ]); // Trigger on financial changes

  // Load plan on component mount
  useEffect(() => {
    const savedPlan = DataService.loadPlan(currentPlanName);
    if (savedPlan) {
      loadStateFromSave(savedPlan);
      setLastSaved(savedPlan.timestamp);
    }
  }, []);

  // Initialize economic alerts on component mount
  useEffect(() => {
    // Generate initial economic alerts
    const alerts = SwissEconomicDataEngine.generateEconomicAlerts();
    setEconomicAlerts(alerts);
  }, [setEconomicAlerts]);

  // Plan management functions
  const handleLoadPlan = (planName) => {
    setCurrentPlanName(planName);
    const savedPlan = DataService.loadPlan(planName);
    if (savedPlan) {
      loadStateFromSave(savedPlan);
      setLastSaved(savedPlan.timestamp);
    }
  };

  const handleDeletePlan = (planName) => {
    DataService.deletePlan(planName);
    if (planName === currentPlanName) {
      setCurrentPlanName("default");
    }
  };

  const handleExport = (format) => {
    const currentState = getCurrentStateForSave();
    if (format === "csv") {
      DataService.exportToCsv(currentState);
    } else if (format === "json") {
      DataService.exportToJson(currentState);
    }
  };

  const handleImport = (data) => {
    loadStateFromSave(data);
    const success = DataService.autoSave(data, currentPlanName);
    if (success) {
      setLastSaved(new Date().toISOString());
    }
  };

  // Get historical snapshots for charts
  const snapshots = DataService.getSnapshots(currentPlanName);

  const addExpense = () =>
    setExpenses([
      ...expenses,
      { id: Date.now(), category: "", amount: "", essential: false },
    ]);
  const removeExpense = (id) =>
    setExpenses(expenses.filter((e) => e.id !== id));
  const updateExpense = (id, field, value) =>
    setExpenses(
      expenses.map((e) => (e.id === id ? { ...e, [field]: value } : e))
    );
  const addSavingsGoal = () =>
    setSavings([...savings, { id: Date.now(), goal: "", amount: "" }]);
  const removeSavingsGoal = (id) =>
    setSavings(savings.filter((s) => s.id !== id));
  const updateSavingsGoal = (id, field, value) => {
    const newSavings = savings.map((s) =>
      s.id === id ? { ...s, [field]: value } : s
    );
    setSavings(newSavings);
    if (currentYear < companyIncomeStartYear) {
      const newSum = newSavings.reduce(
        (sum, s) => sum + (parseFloat(s.amount) || 0),
        0
      );
      const currentNonCompanyIncome =
        currentPrimaryIncome + hsluIncomeAmount + ruagIncomeAmount;
      if (currentNonCompanyIncome > 0) {
        setTargetOverallSavingsRate((newSum / currentNonCompanyIncome) * 100);
      } else if (initialTotalIncome > 0) {
        setTargetOverallSavingsRate((newSum / initialTotalIncome) * 100);
      }
    }
  };

  const getProgressColor = (progress) => {
    if (progress >= 75) return "from-emerald-500 to-green-500";
    if (progress >= 50) return "from-yellow-500 to-orange-500";
    return "from-red-500 to-pink-500";
  };

  const TabButton = ({ id, label, icon, active, onClick }) => (
    <button
      onClick={onClick}
      data-testid={`tab-${id}`}
      className={`flex items-center px-3 py-2 md:px-5 md:py-2.5 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 text-xs sm:text-sm md:text-base whitespace-nowrap ${
        active
          ? darkMode
            ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md"
            : "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-md"
          : darkMode
          ? "bg-gray-700/70 text-gray-300 hover:bg-gray-600/70"
          : "bg-white/90 text-gray-600 hover:bg-gray-200/70 shadow-sm"
      }`}
    >
      <span className="mr-1.5 hidden sm:inline-block text-base">{icon}</span>{" "}
      {label}
    </button>
  );

  const SecondaryTabButton = ({
    id,
    label,
    icon,
    active,
    onClick,
    darkMode,
  }) => (
    <button
      onClick={onClick}
      data-testid={`secondary-tab-${id}`}
      className={`flex items-center px-2 py-1.5 md:px-3 md:py-2 rounded-md font-medium transition-all duration-200 text-xs sm:text-sm whitespace-nowrap ${
        active
          ? darkMode
            ? "bg-blue-600/80 text-white shadow-sm"
            : "bg-blue-500/80 text-white shadow-sm"
          : darkMode
          ? "bg-gray-600/50 text-gray-300 hover:bg-gray-600/70"
          : "bg-gray-200/70 text-gray-600 hover:bg-gray-300/70"
      }`}
    >
      <span className="mr-1 text-sm">{icon}</span> {label}
    </button>
  );

  const budgetSummaryMetricsData = useMemo(
    () => [
      {
        label: "Total Income",
        getValue: () => totalMonthlyIncome,
        icon: "💰",
        iconColorLight: "text-blue-700",
        iconColorDark: "text-blue-300",
        valueColorLight: "text-blue-600",
        valueColorDark: "text-blue-400",
      },
      {
        label: "Essential Expenses",
        getValue: () => essentialExpenses,
        getPercentage: () =>
          totalMonthlyIncome > 0
            ? (essentialExpenses / totalMonthlyIncome) * 100
            : 0,
        icon: "📊",
        iconColorLight: "text-red-700",
        iconColorDark: "text-red-300",
        valueColorLight: "text-red-600",
        valueColorDark: "text-red-400",
      },
      {
        label: "Discretionary",
        getValue: () => nonEssentialExpenses,
        getPercentage: () =>
          totalMonthlyIncome > 0
            ? (nonEssentialExpenses / totalMonthlyIncome) * 100
            : 0,
        icon: "⭐",
        iconColorLight: "text-orange-700",
        iconColorDark: "text-orange-300",
        valueColorLight: "text-orange-600",
        valueColorDark: "text-orange-400",
      },
      {
        label: "Total Savings",
        getValue: () => totalSavingsDisplay,
        getPercentage: () => savingsRate,
        icon: "📈",
        iconColorLight: "text-emerald-700",
        iconColorDark: "text-emerald-300",
        valueColorLight: "text-emerald-600",
        valueColorDark: "text-emerald-400",
      },
      {
        label: "Remaining/Surplus",
        getValue: () => remaining,
        icon: "⚡",
        iconColorLight: remaining >= 0 ? "text-green-700" : "text-pink-700",
        iconColorDark: remaining >= 0 ? "text-green-300" : "text-pink-300",
        valueColorLight: remaining >= 0 ? "text-green-600" : "text-pink-600",
        valueColorDark: remaining >= 0 ? "text-green-400" : "text-pink-400",
      },
    ],
    [
      darkMode,
      totalMonthlyIncome,
      essentialExpenses,
      nonEssentialExpenses,
      totalSavingsDisplay,
      savingsRate,
      remaining,
    ]
  );

  return (
    <div
      className={`min-h-screen transition-all duration-500 font-sans ${
        darkMode
          ? "bg-gradient-to-br from-gray-900 via-purple-900/25 to-blue-900/25 text-gray-200"
          : "bg-gradient-to-br from-blue-50 via-purple-50/40 to-pink-50 text-gray-800"
      }`}
    >
      {/* Onboarding Wizard */}
      {showOnboarding && (
        <OnboardingWizard
          darkMode={darkMode}
          onComplete={handleOnboardingComplete}
          onSkip={() => setShowOnboarding(false)}
          userProgress={userProgress}
          existingData={{
            basicInfo: {
              age: currentAge,
              retirementAge: retirementAge,
              targetIncome: targetRetirementIncome,
            },
            income: {
              primaryIncome: currentPrimaryIncome,
            },
            expenses: expenses,
            savings: savings,
            swissConfig: {
              canton: selectedCanton,
            },
          }}
        />
      )}

      <div className="max-w-7xl mx-auto p-3 sm:p-4 md:p-6">
        <div className="mb-5 md:mb-8 relative">
          <div className="flex flex-col sm:flex-row justify-between items-start mb-4 md:mb-6">
            <div className="relative mb-3 sm:mb-0">
              <h1
                className={`text-3xl sm:text-4xl md:text-5xl font-black mb-1.5 flex items-center bg-clip-text text-transparent ${
                  darkMode
                    ? "bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400"
                    : "bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600"
                }`}
              >
                <div className="relative mr-2 md:mr-3">
                  <span className="text-green-500 animate-pulse text-3xl">
                    💰
                  </span>
                  <span className="absolute -top-0.5 -right-0.5 text-yellow-400 animate-bounce text-sm">
                    ⭐
                  </span>
                </div>
                {t("appName")}{" "}
                <span className="text-xs md:text-sm ml-2 bg-gradient-to-r from-yellow-400 to-orange-400 px-1.5 py-0.5 rounded-full text-black font-bold">
                  Pro
                </span>
              </h1>
              <p
                className={`text-sm md:text-base ${
                  darkMode ? "text-gray-400" : "text-gray-600"
                } font-medium`}
              >
                🇨🇭 {t("tagline")}
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <LanguageSwitcher darkMode={darkMode} />
              <button
                onClick={() => setShowOnboarding(true)}
                className={`p-2.5 rounded-lg transition-all duration-300 transform hover:scale-110 ${
                  darkMode
                    ? "bg-gradient-to-r from-green-500 to-emerald-500 shadow-sm shadow-green-500/20"
                    : "bg-gradient-to-r from-green-600 to-emerald-600 shadow-sm shadow-green-500/20"
                }`}
                title="Restart Setup Wizard"
              >
                <span className="text-white text-lg">🚀</span>
              </button>
              <button
                onClick={() => setDarkMode(!darkMode)}
                className={`p-2.5 rounded-lg transition-all duration-300 transform hover:scale-110 ${
                  darkMode
                    ? "bg-gradient-to-r from-yellow-400 to-orange-400 shadow-sm shadow-yellow-500/20"
                    : "bg-gradient-to-r from-purple-600 to-blue-600 shadow-sm shadow-purple-500/20"
                }`}
                title={darkMode ? t("theme.light") : t("theme.dark")}
              >
                <span className="text-white text-lg">
                  {darkMode ? "☀️" : "🌙"}
                </span>
              </button>
            </div>
          </div>
          <div
            className={`grid grid-cols-2 sm:grid-cols-4 gap-2 md:gap-3 text-xs md:text-sm p-2.5 md:p-3 rounded-lg mb-4 md:mb-6 ${
              darkMode ? "bg-gray-800/60" : "bg-white/80 shadow-sm"
            }`}
          >
            {[
              {
                icon: "📈",
                label: `${savingsRate.toFixed(1)}% ${t("metrics.savingsRate")}`,
                color: darkMode ? "text-emerald-400" : "text-emerald-600",
                testId: "savings-rate",
              },
              {
                icon: "🎯",
                label: `${(finalProjection?.fireProgress || 0).toFixed(0)}% ${t(
                  "metrics.fireProgress"
                )}`,
                color: darkMode ? "text-blue-400" : "text-blue-600",
              },
              {
                icon: "📅",
                label: `${Math.max(0, retirementAge - currentAge)} ${t(
                  "metrics.yearsToRetire"
                )}`,
                color: darkMode ? "text-purple-400" : "text-purple-600",
              },
              {
                icon: "💰",
                label: `${formatCurrency(totalMonthlyIncome)} ${t(
                  "financial:income.title"
                )}`,
                color: darkMode ? "text-yellow-400" : "text-yellow-600",
              },
            ].map((stat) => (
              <div
                key={stat.label}
                className={`flex items-center ${stat.color}`}
                data-testid={stat.testId}
              >
                <span className="mr-1 text-sm">{stat.icon}</span>
                <span className="font-medium">{stat.label}</span>
              </div>
            ))}
          </div>
          {/* Smart Navigation - Primary Tabs */}
          <div className="flex flex-wrap gap-1.5 md:gap-2.5 mb-4">
            <TabButton
              id="dashboard"
              label="Dashboard"
              icon="🏠"
              active={activeTab === "dashboard"}
              onClick={() => setActiveTab("dashboard")}
            />
            <TabButton
              id="planning"
              label="Planning"
              icon="💰"
              active={activeTab === "planning"}
              onClick={() => setActiveTab("planning")}
            />
            <TabButton
              id="analysis"
              label="Analysis"
              icon="📊"
              active={activeTab === "analysis"}
              onClick={() => setActiveTab("analysis")}
            />
            <TabButton
              id="advanced"
              label="Advanced"
              icon="⚙️"
              active={activeTab === "advanced"}
              onClick={() => setActiveTab("advanced")}
            />
          </div>

          {/* Secondary Navigation - Context-aware sub-tabs */}
          {activeTab === "planning" && (
            <div className="flex flex-wrap gap-1 md:gap-1.5 mb-4">
              <SecondaryTabButton
                id="income"
                label="Income"
                icon="💰"
                active={secondaryTab === "income"}
                onClick={() => setSecondaryTab("income")}
                darkMode={darkMode}
              />
              <SecondaryTabButton
                id="expenses"
                label="Expenses"
                icon="🎯"
                active={secondaryTab === "expenses"}
                onClick={() => setSecondaryTab("expenses")}
                darkMode={darkMode}
              />
              <SecondaryTabButton
                id="savings"
                label="Savings"
                icon="⭐"
                active={secondaryTab === "savings"}
                onClick={() => setSecondaryTab("savings")}
                darkMode={darkMode}
              />
              <SecondaryTabButton
                id="goals"
                label="Goals"
                icon="🎯"
                active={secondaryTab === "goals"}
                onClick={() => setSecondaryTab("goals")}
                darkMode={darkMode}
              />
            </div>
          )}

          {activeTab === "analysis" && (
            <div className="flex flex-wrap gap-1 md:gap-1.5 mb-4">
              <SecondaryTabButton
                id="projections"
                label="Projections"
                icon="📈"
                active={secondaryTab === "projections"}
                onClick={() => setSecondaryTab("projections")}
                darkMode={darkMode}
              />
              <SecondaryTabButton
                id="taxOptimization"
                label="Tax Optimizer"
                icon="🇨🇭"
                active={secondaryTab === "taxOptimization"}
                onClick={() => setSecondaryTab("taxOptimization")}
                darkMode={darkMode}
              />
              <SecondaryTabButton
                id="scenarios"
                label="Scenarios"
                icon="🧠"
                active={secondaryTab === "scenarios"}
                onClick={() => setSecondaryTab("scenarios")}
                darkMode={darkMode}
              />
              <SecondaryTabButton
                id="visualizations"
                label="Charts"
                icon="📊"
                active={secondaryTab === "visualizations"}
                onClick={() => setSecondaryTab("visualizations")}
                darkMode={darkMode}
              />
            </div>
          )}
        </div>

        {activeTab === "analysis" && (
          <div className="space-y-6">
            {secondaryTab === "taxOptimization" && (
              <div className="space-y-5 md:space-y-6">
                {/* Swiss Tax Configuration */}
                <div
                  className={`p-5 md:p-6 rounded-xl ${
                    darkMode
                      ? "bg-gray-800/70 border border-red-500/25"
                      : "bg-white/90 border border-red-200 shadow-md"
                  } backdrop-blur-sm`}
                >
                  <h3
                    className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                      darkMode ? "text-white" : "text-gray-800"
                    } flex items-center`}
                  >
                    <span className="mr-2 text-red-500 text-xl">🇨🇭</span>{" "}
                    {t("swiss:taxes.title")}
                  </h3>

                  <div className="grid md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <label
                        className={`block text-sm font-medium mb-2 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        Canton
                      </label>
                      <select
                        value={selectedCanton}
                        onChange={(e) => setSelectedCanton(e.target.value)}
                        data-testid="canton-select"
                        className={`w-full p-3 rounded-lg border ${
                          darkMode
                            ? "bg-gray-700 border-gray-600 text-white"
                            : "bg-white border-gray-300 text-gray-800"
                        } focus:ring-2 focus:ring-red-500`}
                      >
                        {Object.entries(SwissTaxEngine.cantons).map(
                          ([code, canton]) => (
                            <option key={code} value={code}>
                              {canton.name} ({code})
                            </option>
                          )
                        )}
                      </select>
                    </div>

                    <div>
                      <label
                        className={`block text-sm font-medium mb-2 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        Civil Status
                      </label>
                      <select
                        value={civilStatus}
                        onChange={(e) => setCivilStatus(e.target.value)}
                        data-testid="civil-status-select"
                        className={`w-full p-3 rounded-lg border ${
                          darkMode
                            ? "bg-gray-700 border-gray-600 text-white"
                            : "bg-white border-gray-300 text-gray-800"
                        } focus:ring-2 focus:ring-red-500`}
                      >
                        <option value="single">Single</option>
                        <option value="married">Married</option>
                      </select>
                    </div>

                    <div>
                      <label
                        className={`block text-sm font-medium mb-2 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        Pension Fund (BVG)
                      </label>
                      <select
                        value={hasSecondPillar ? "yes" : "no"}
                        onChange={(e) =>
                          setHasSecondPillar(e.target.value === "yes")
                        }
                        className={`w-full p-3 rounded-lg border ${
                          darkMode
                            ? "bg-gray-700 border-gray-600 text-white"
                            : "bg-white border-gray-300 text-gray-800"
                        } focus:ring-2 focus:ring-red-500`}
                      >
                        <option value="yes">Yes (Employee)</option>
                        <option value="no">No (Self-employed)</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label
                      className={`block text-sm font-medium mb-2 ${
                        darkMode ? "text-gray-300" : "text-gray-700"
                      }`}
                    >
                      Current Pillar 3a Balance
                    </label>
                    <div className="relative">
                      <span
                        className={`absolute left-3 top-1/2 -translate-y-1/2 text-sm font-medium ${
                          darkMode ? "text-gray-400" : "text-gray-500"
                        }`}
                      >
                        CHF
                      </span>
                      <input
                        type="number"
                        value={currentPillar3a}
                        onChange={(e) => setCurrentPillar3a(e.target.value)}
                        className={`w-full pl-12 pr-3 py-3 rounded-lg border ${
                          darkMode
                            ? "bg-gray-700 border-gray-600 text-white"
                            : "bg-white border-gray-300 text-gray-800"
                        } focus:ring-2 focus:ring-red-500`}
                        placeholder="0"
                      />
                    </div>
                  </div>
                </div>

                {/* Current Tax Situation */}
                <div
                  className={`p-5 md:p-6 rounded-xl ${
                    darkMode
                      ? "bg-gray-800/70 border border-blue-500/25"
                      : "bg-white/90 border border-blue-200 shadow-md"
                  } backdrop-blur-sm`}
                >
                  <h3
                    className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                      darkMode ? "text-white" : "text-gray-800"
                    } flex items-center`}
                  >
                    <span className="mr-2 text-blue-500 text-xl">📊</span>{" "}
                    Current Tax Situation
                  </h3>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div
                      className={`p-4 rounded-lg ${
                        darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                      }`}
                    >
                      <h4
                        className={`text-lg font-semibold mb-3 ${
                          darkMode ? "text-gray-200" : "text-gray-700"
                        }`}
                      >
                        Annual Tax Breakdown
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Federal Tax:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-red-400" : "text-red-600"
                            }`}
                          >
                            {formatCurrency(
                              swissTaxAnalysis.currentTaxSituation.federalTax
                            )}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Cantonal Tax:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-red-400" : "text-red-600"
                            }`}
                          >
                            {formatCurrency(
                              swissTaxAnalysis.currentTaxSituation.cantonalTax
                            )}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Wealth Tax:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-red-400" : "text-red-600"
                            }`}
                          >
                            {formatCurrency(
                              swissTaxAnalysis.currentTaxSituation.wealthTax
                            )}
                          </span>
                        </div>
                        <div className="border-t pt-2 mt-2">
                          <div className="flex justify-between">
                            <span
                              className={`text-base font-semibold ${
                                darkMode ? "text-white" : "text-gray-800"
                              }`}
                            >
                              Total Tax (Annual):
                            </span>
                            <span
                              className={`text-base font-bold ${
                                darkMode ? "text-red-300" : "text-red-700"
                              }`}
                            >
                              {formatCurrency(
                                swissTaxAnalysis.currentTaxSituation.totalTax
                              )}
                            </span>
                          </div>
                          <div className="flex justify-between mt-1">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Monthly Tax:
                            </span>
                            <span
                              data-testid="monthly-tax"
                              className={`text-sm font-medium ${
                                darkMode ? "text-red-400" : "text-red-600"
                              }`}
                            >
                              {formatCurrency(
                                swissTaxAnalysis.currentTaxSituation.totalTax /
                                  12
                              )}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div
                      className={`p-4 rounded-lg ${
                        darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                      }`}
                    >
                      <h4
                        className={`text-lg font-semibold mb-3 ${
                          darkMode ? "text-gray-200" : "text-gray-700"
                        }`}
                      >
                        Tax Rates
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Effective Rate:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-blue-400" : "text-blue-600"
                            }`}
                          >
                            {swissTaxAnalysis.currentTaxSituation.effectiveRate.toFixed(
                              1
                            )}
                            %
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Marginal Rate:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-blue-400" : "text-blue-600"
                            }`}
                          >
                            {swissTaxAnalysis.currentTaxSituation.marginalRate.toFixed(
                              1
                            )}
                            %
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Canton:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-green-400" : "text-green-600"
                            }`}
                          >
                            {SwissTaxEngine.cantons[selectedCanton].name}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Net Income:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-green-400" : "text-green-600"
                            }`}
                          >
                            {formatCurrency(
                              totalMonthlyIncome * 12 -
                                swissTaxAnalysis.currentTaxSituation.totalTax
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === "advanced" && (
          <div className="space-y-6">
            <div className="flex flex-wrap gap-1 md:gap-1.5 mb-4">
              <SecondaryTabButton
                id="economicData"
                label="Economic Data"
                icon="📈"
                active={secondaryTab === "economicData"}
                onClick={() => setSecondaryTab("economicData")}
                darkMode={darkMode}
              />
              <SecondaryTabButton
                id="analytics"
                label="Analytics"
                icon="🧠"
                active={secondaryTab === "analytics"}
                onClick={() => setSecondaryTab("analytics")}
                darkMode={darkMode}
              />
              <SecondaryTabButton
                id="reports"
                label="Reports"
                icon="📄"
                active={secondaryTab === "reports"}
                onClick={() => setSecondaryTab("reports")}
                darkMode={darkMode}
              />
              <SecondaryTabButton
                id="data"
                label="Data"
                icon="💾"
                active={secondaryTab === "data"}
                onClick={() => setSecondaryTab("data")}
                darkMode={darkMode}
              />
            </div>

            {secondaryTab === "data" && (
              <div className="space-y-5 md:space-y-6">
                {/* Data Export/Import */}
                <div
                  className={`p-5 md:p-6 rounded-xl ${
                    darkMode
                      ? "bg-gray-800/70 border border-blue-500/25"
                      : "bg-white/90 border border-blue-200 shadow-md"
                  } backdrop-blur-sm`}
                >
                  <h3
                    className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                      darkMode ? "text-white" : "text-gray-800"
                    } flex items-center`}
                  >
                    <span className="mr-2 text-blue-500 text-xl">💾</span> Data
                    Management
                  </h3>

                  <div className="grid md:grid-cols-2 gap-4">
                    {/* Export Section */}
                    <div
                      className={`p-4 rounded-lg ${
                        darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                      }`}
                    >
                      <h4
                        className={`text-lg font-semibold mb-3 ${
                          darkMode ? "text-gray-200" : "text-gray-700"
                        }`}
                      >
                        Export Data
                      </h4>
                      <p
                        className={`text-sm mb-4 ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Download your financial data for backup or analysis in
                        external tools.
                      </p>
                      <div className="space-y-3">
                        <button
                          onClick={() => handleExport("json")}
                          data-testid="export-data"
                          className={`w-full px-4 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 ${
                            darkMode
                              ? "bg-blue-600 text-white hover:bg-blue-700"
                              : "bg-blue-500 text-white hover:bg-blue-600"
                          } shadow-sm`}
                        >
                          <span className="mr-2">📄</span>
                          Export as JSON
                        </button>
                        <button
                          onClick={() => handleExport("csv")}
                          className={`w-full px-4 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 ${
                            darkMode
                              ? "bg-green-600 text-white hover:bg-green-700"
                              : "bg-green-500 text-white hover:bg-green-600"
                          } shadow-sm`}
                        >
                          <span className="mr-2">📊</span>
                          Export as CSV
                        </button>
                      </div>
                    </div>

                    {/* Import Section */}
                    <div
                      className={`p-4 rounded-lg ${
                        darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                      }`}
                    >
                      <h4
                        className={`text-lg font-semibold mb-3 ${
                          darkMode ? "text-gray-200" : "text-gray-700"
                        }`}
                      >
                        Import Data
                      </h4>
                      <p
                        className={`text-sm mb-4 ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Restore your financial data from a previously exported
                        JSON file.
                      </p>
                      <input
                        type="file"
                        accept=".json"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            const reader = new FileReader();
                            reader.onload = (event) => {
                              try {
                                const data = JSON.parse(
                                  event.target?.result as string
                                );
                                handleImport(data);
                                alert("Data imported successfully!");
                              } catch (error) {
                                alert(
                                  "Error importing data. Please check the file format."
                                );
                              }
                            };
                            reader.readAsText(file);
                          }
                        }}
                        className={`w-full px-4 py-3 rounded-lg border-2 border-dashed transition-colors ${
                          darkMode
                            ? "border-gray-600 bg-gray-800 text-gray-300 hover:border-gray-500"
                            : "border-gray-300 bg-gray-50 text-gray-700 hover:border-gray-400"
                        }`}
                      />
                    </div>
                  </div>

                  {/* Data Summary */}
                  <div
                    className={`mt-6 p-4 rounded-lg ${
                      darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                    }`}
                  >
                    <h4
                      className={`text-lg font-semibold mb-3 ${
                        darkMode ? "text-gray-200" : "text-gray-700"
                      }`}
                    >
                      Current Data Summary
                    </h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span
                          className={`block font-medium ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          Monthly Income:
                        </span>
                        <span
                          className={`font-bold ${
                            darkMode ? "text-green-400" : "text-green-600"
                          }`}
                        >
                          {formatCurrency(totalMonthlyIncome)}
                        </span>
                      </div>
                      <div>
                        <span
                          className={`block font-medium ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          Savings Rate:
                        </span>
                        <span
                          className={`font-bold ${
                            darkMode ? "text-blue-400" : "text-blue-600"
                          }`}
                        >
                          {savingsRate.toFixed(1)}%
                        </span>
                      </div>
                      <div>
                        <span
                          className={`block font-medium ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          Current Age:
                        </span>
                        <span
                          className={`font-bold ${
                            darkMode ? "text-purple-400" : "text-purple-600"
                          }`}
                        >
                          {currentAge}
                        </span>
                      </div>
                      <div>
                        <span
                          className={`block font-medium ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          Retirement Age:
                        </span>
                        <span
                          className={`font-bold ${
                            darkMode ? "text-orange-400" : "text-orange-600"
                          }`}
                        >
                          {retirementAge}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === "dashboard" && (
          <SmartDashboard
            darkMode={darkMode}
            userProgress={userProgress}
            financialData={{
              totalMonthlyIncome,
              totalExpenses,
              savingsRate,
              fireProgress: finalProjection?.fireProgress || 0,
              monthsToFire: finalProjection?.monthsToFire || 0,
              currentBalance: finalProjection?.totalBalance || 0,
              currentAge:
                typeof currentAge === "string"
                  ? parseInt(currentAge)
                  : currentAge,
              retirementAge:
                typeof retirementAge === "string"
                  ? parseInt(retirementAge)
                  : retirementAge,
              finalProjection,
            }}
            onNavigate={(tab, secondaryTab) => {
              setActiveTab(tab);
              if (secondaryTab) setSecondaryTab(secondaryTab);
            }}
            onShowOnboarding={() => setShowOnboarding(true)}
          />
        )}

        {activeTab === "planning" && (
          <div className="space-y-6">
            {secondaryTab === "income" && (
              <div
                className={`p-5 md:p-6 rounded-xl ${
                  darkMode
                    ? "bg-gray-800/70 border border-blue-500/25"
                    : "bg-white/90 border border-blue-200 shadow-md"
                } backdrop-blur-sm`}
              >
                <h3
                  className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                    darkMode ? "text-white" : "text-gray-800"
                  } flex items-center`}
                >
                  <span className="mr-2 text-yellow-400 text-xl">⚡</span>{" "}
                  {t("financial:income.title")}
                </h3>
                <div className="grid sm:grid-cols-2 gap-3 md:gap-4 mb-3 md:mb-4">
                  <div>
                    <label
                      className={`block text-xs sm:text-sm font-medium mb-1 ${
                        darkMode ? "text-gray-300" : "text-gray-700"
                      } flex items-center`}
                    >
                      <span className="mr-1 text-green-500 text-base">💰</span>{" "}
                      {t("financial:income.primaryEmployment")} (
                      {t("time.monthly")})
                    </label>
                    <div className="relative">
                      <span
                        className={`absolute left-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                          darkMode ? "text-gray-400" : "text-gray-500"
                        }`}
                      >
                        CHF
                      </span>
                      <input
                        type="number"
                        value={monthlyIncome}
                        onChange={(e) => setMonthlyIncome(e.target.value)}
                        data-testid="monthly-income"
                        className={`w-full pl-10 pr-3 py-2 rounded-md text-sm md:text-base font-semibold border ${
                          darkMode
                            ? "bg-gray-700 border-gray-600 text-white focus:border-blue-400"
                            : "bg-gray-50 border-gray-300 text-gray-800 focus:border-blue-500"
                        } transition-colors focus:ring-1 focus:ring-blue-500/30`}
                      />
                    </div>
                    <div className="mt-2">
                      <label
                        className={`block text-xs font-medium mb-1 ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        {t("financial:income.workTimePercentage")}:{" "}
                        {incomePercentage}%
                      </label>
                      <input
                        type="range"
                        min="70"
                        max="100"
                        step="10"
                        value={incomePercentage}
                        onChange={(e) =>
                          setIncomePercentage(parseInt(e.target.value))
                        }
                        className="w-full h-2 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-green-500"
                        style={{
                          background: `linear-gradient(to right, ${
                            darkMode ? "#22c55e" : "#16a34a"
                          } 0%, ${darkMode ? "#22c55e" : "#16a34a"} ${
                            ((incomePercentage - 70) / 30) * 100
                          }%, ${darkMode ? "#374151" : "#d1d5db"} ${
                            ((incomePercentage - 70) / 30) * 100
                          }%, ${darkMode ? "#374151" : "#d1d5db"} 100%)`,
                        }}
                      />
                      <div className="flex justify-between text-xs mt-1">
                        <span
                          className={`${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          70%
                        </span>
                        <span
                          className={`font-medium ${
                            darkMode ? "text-green-400" : "text-green-600"
                          }`}
                        >
                          Effective: {formatCurrency(currentPrimaryIncome)}
                        </span>
                        <span
                          className={`${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          100%
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label
                      className={`block text-xs sm:text-sm font-medium mb-1 ${
                        darkMode ? "text-gray-300" : "text-gray-700"
                      } flex items-center`}
                    >
                      <span className="mr-1 text-purple-500 text-base">⭐</span>{" "}
                      {t("financial:income.companyIncome")} ({t("time.monthly")}
                      )
                    </label>
                    <div className="relative">
                      <span
                        className={`absolute left-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                          darkMode ? "text-gray-400" : "text-gray-500"
                        }`}
                      >
                        CHF
                      </span>
                      <input
                        type="number"
                        value={companyIncome}
                        onChange={(e) => setCompanyIncome(e.target.value)}
                        data-testid="company-income"
                        className={`w-full pl-10 pr-3 py-2 rounded-md text-sm md:text-base font-semibold border ${
                          darkMode
                            ? "bg-gray-700 border-gray-600 text-white focus:border-purple-400"
                            : "bg-gray-50 border-gray-300 text-gray-800 focus:border-purple-500"
                        } transition-colors focus:ring-1 focus:ring-purple-500/30`}
                      />
                    </div>
                  </div>
                </div>
                <div
                  className={`p-3 rounded-md ${
                    darkMode ? "bg-gray-700/50" : "bg-gray-100/80"
                  } border ${
                    darkMode ? "border-gray-600/40" : "border-gray-200"
                  } mb-3 md:mb-4`}
                >
                  <h4
                    className={`text-xs sm:text-sm font-semibold mb-2 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    } flex items-center`}
                  >
                    <span className="mr-1.5 text-indigo-400 text-base">📅</span>{" "}
                    Company Income Dynamics
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label
                        className={`block text-xs font-medium mb-0.5 ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        Start Year
                      </label>
                      <input
                        type="number"
                        value={companyIncomeStartYear}
                        onChange={(e) =>
                          setCompanyIncomeStartYear(
                            parseInt(e.target.value) || 2025
                          )
                        }
                        min={currentYear}
                        className={`w-full p-1.5 rounded text-xs font-medium border ${
                          darkMode
                            ? "bg-gray-600 border-gray-500"
                            : "bg-white border-gray-300"
                        }`}
                      />
                      <div
                        className={`text-2xs mt-0.5 ${
                          darkMode ? "text-indigo-400" : "text-indigo-600"
                        }`}
                      >
                        Age {companyIncomeStartAge}
                      </div>
                    </div>
                    <div>
                      <label
                        className={`block text-xs font-medium mb-0.5 ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        Growth (% p.a.)
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        value={companyIncomeGrowthRate}
                        onChange={(e) =>
                          setCompanyIncomeGrowthRate(
                            parseFloat(e.target.value) || 0
                          )
                        }
                        className={`w-full p-1.5 rounded text-xs font-medium border ${
                          darkMode
                            ? "bg-gray-600 border-gray-500"
                            : "bg-white border-gray-300"
                        }`}
                      />
                    </div>
                  </div>
                </div>
                <div
                  className={`p-3 rounded-md ${
                    darkMode ? "bg-gray-700/50" : "bg-gray-100/80"
                  } border ${
                    darkMode ? "border-gray-600/40" : "border-gray-200"
                  } mb-3 md:mb-4`}
                >
                  <h4
                    className={`text-xs sm:text-sm font-semibold mb-2 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    } flex items-center`}
                  >
                    <span className="mr-1.5 text-emerald-500 text-base">
                      🏆
                    </span>{" "}
                    Additional Income (Annual)
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label
                        className={`block text-xs font-medium mb-0.5 ${
                          darkMode ? "text-emerald-300" : "text-emerald-700"
                        }`}
                      >
                        HSLU (Annual)
                      </label>
                      <div className="relative">
                        <span
                          className={`absolute left-2 top-1/2 -translate-y-1/2 text-xs ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          CHF
                        </span>
                        <input
                          type="number"
                          value={hsluIncome}
                          onChange={(e) => setHsluIncome(e.target.value)}
                          data-testid="hslu-income"
                          className={`w-full pl-9 pr-2 py-1.5 rounded text-xs font-medium border ${
                            darkMode
                              ? "bg-gray-600 border-gray-500"
                              : "bg-white border-gray-300"
                          }`}
                        />
                      </div>
                      <div
                        className={`text-2xs mt-0.5 ${
                          darkMode ? "text-emerald-400" : "text-emerald-600"
                        }`}
                      >
                        ~{formatCurrency(hsluIncomeAmount)}/mo
                      </div>
                    </div>
                    <div>
                      <label
                        className={`block text-xs font-medium mb-0.5 ${
                          darkMode ? "text-blue-300" : "text-blue-700"
                        }`}
                      >
                        RUAG (Annual)
                      </label>
                      <div className="relative">
                        <span
                          className={`absolute left-2 top-1/2 -translate-y-1/2 text-xs ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          CHF
                        </span>
                        <input
                          type="number"
                          value={ruagIncome}
                          onChange={(e) => setRuagIncome(e.target.value)}
                          data-testid="ruag-income"
                          className={`w-full pl-9 pr-2 py-1.5 rounded text-xs font-medium border ${
                            darkMode
                              ? "bg-gray-600 border-gray-500"
                              : "bg-white border-gray-300"
                          }`}
                        />
                      </div>
                      <div
                        className={`text-2xs mt-0.5 ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        ~{formatCurrency(ruagIncomeAmount)}/mo
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className={`p-3 md:p-4 rounded-lg ${
                    darkMode
                      ? "bg-gradient-to-r from-green-800/60 to-emerald-800/60 border border-green-500/30"
                      : "bg-gradient-to-r from-green-100 to-emerald-100 border border-green-300"
                  } shadow-sm`}
                >
                  <div className="flex flex-col sm:flex-row items-center justify-between">
                    <h4
                      className={`text-base sm:text-lg md:text-xl font-bold ${
                        darkMode ? "text-green-300" : "text-green-700"
                      } flex items-center`}
                    >
                      <span className="mr-1.5 text-yellow-400 text-lg">🚀</span>{" "}
                      Total Monthly Income
                    </h4>
                    <div className="text-right mt-1 sm:mt-0">
                      <div
                        className={`text-xl sm:text-2xl md:text-3xl font-black ${
                          darkMode ? "text-green-400" : "text-green-600"
                        }`}
                      >
                        {formatCurrency(totalMonthlyIncome)}
                      </div>
                      <div
                        className={`text-xs sm:text-sm font-medium ${
                          darkMode ? "text-green-300" : "text-green-700"
                        }`}
                      >
                        {formatCurrency(totalMonthlyIncome * 12)}/year
                      </div>
                    </div>
                  </div>
                  {incomePercentage < 100 && (
                    <div
                      className={`mt-2 p-2 rounded text-xs ${
                        darkMode
                          ? "bg-yellow-800/40 text-yellow-200 border border-yellow-600/30"
                          : "bg-yellow-50 text-yellow-800 border border-yellow-200"
                      }`}
                    >
                      ⚠️ <strong>Part-time Impact:</strong> Reduced to{" "}
                      {incomePercentage}% work time. Annual reduction:{" "}
                      {formatCurrency(
                        (parseFloat(monthlyIncome) || 0) *
                          (1 - incomePercentage / 100) *
                          12
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {secondaryTab === "goals" && (
              <div className="space-y-5 md:space-y-6">
                <div
                  className={`p-5 md:p-6 rounded-xl ${
                    darkMode
                      ? "bg-gray-800/70 border-purple-500/25"
                      : "bg-white/90 border-purple-200 shadow-md"
                  } backdrop-blur-sm`}
                >
                  <h3
                    className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                      darkMode ? "text-white" : "text-gray-800"
                    } flex items-center`}
                  >
                    <span className="mr-2 text-purple-400 text-xl">🎯</span>{" "}
                    Retirement & Savings Configuration
                  </h3>
                  <div className="grid md:grid-cols-2 gap-3 md:gap-4 mb-3 md:mb-4">
                    <div>
                      <label
                        className={`block text-xs sm:text-sm font-medium mb-1 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        } flex items-center`}
                      >
                        <span className="mr-1 text-green-400 text-sm">💎</span>{" "}
                        Target Monthly Retirement Income
                      </label>
                      <div className="relative">
                        <span
                          className={`absolute left-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          CHF
                        </span>
                        <input
                          type="number"
                          value={targetRetirementIncome}
                          onChange={(e) =>
                            setTargetRetirementIncome(e.target.value)
                          }
                          className={`w-full pl-10 pr-3 py-2 rounded-md text-sm md:text-base font-semibold border ${
                            darkMode
                              ? "bg-gray-700 border-gray-600 text-white focus:border-purple-400"
                              : "bg-gray-50 border-gray-300 text-gray-800 focus:border-purple-500"
                          } transition-colors focus:ring-1 focus:ring-purple-500/30`}
                        />
                      </div>
                    </div>
                    <div>
                      <label
                        className={`block text-xs sm:text-sm font-medium mb-1 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        } flex items-center`}
                      >
                        <span className="mr-1 text-teal-400 text-sm">%</span>{" "}
                        Target Overall Savings Rate
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          step="0.1"
                          value={targetOverallSavingsRate}
                          onChange={(e) =>
                            setTargetOverallSavingsRate(
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className={`w-full pl-3 pr-8 py-2 rounded-md text-sm md:text-base font-semibold border ${
                            darkMode
                              ? "bg-gray-700 border-gray-600 text-white focus:border-teal-400"
                              : "bg-gray-50 border-gray-300 text-gray-800 focus:border-teal-500"
                          } transition-colors focus:ring-1 focus:ring-teal-500/30`}
                        />
                        <span
                          className={`absolute right-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          %
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="grid md:grid-cols-2 gap-3 md:gap-4 mb-3 md:mb-4">
                    <div>
                      <label
                        className={`block text-xs sm:text-sm font-medium mb-1 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        Current Total Savings (Portfolio Start)
                      </label>
                      <div className="relative">
                        <span
                          className={`absolute left-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          CHF
                        </span>
                        <input
                          type="number"
                          value={currentSavings}
                          onChange={(e) => setCurrentSavings(e.target.value)}
                          className={`w-full pl-10 pr-3 py-2 rounded-md text-sm md:text-base font-semibold border ${
                            darkMode
                              ? "bg-gray-700 border-gray-600 text-white focus:border-indigo-400"
                              : "bg-gray-50 border-gray-300 text-gray-800 focus:border-indigo-500"
                          } transition-colors focus:ring-1 focus:ring-indigo-500/30`}
                        />
                      </div>
                    </div>
                    <div>
                      <label
                        className={`block text-xs sm:text-sm font-medium mb-1 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        } flex items-center`}
                      >
                        <span className="mr-1 text-blue-400 text-sm">🏛️</span>{" "}
                        Current Pension Leaving Benefits
                      </label>
                      <div className="relative">
                        <span
                          className={`absolute left-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          CHF
                        </span>
                        <input
                          type="number"
                          value={currentPensionLeavingBenefits}
                          onChange={(e) =>
                            setCurrentPensionLeavingBenefits(e.target.value)
                          }
                          className={`w-full pl-10 pr-3 py-2 rounded-md text-sm md:text-base font-semibold border ${
                            darkMode
                              ? "bg-gray-700 border-gray-600 text-white focus:border-blue-400"
                              : "bg-gray-50 border-gray-300 text-gray-800 focus:border-blue-500"
                          } transition-colors focus:ring-1 focus:ring-blue-500/30`}
                        />
                      </div>
                      <div
                        className={`text-2xs mt-0.5 ${
                          darkMode ? "text-blue-300" : "text-blue-600"
                        }`}
                      >
                        Contribution:{" "}
                        {formatCurrency(adjustedPensionContribution)}
                        /mo ({incomePercentage}% of{" "}
                        {formatCurrency(basePensionContribution)})
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 mb-3 md:mb-4">
                    <div>
                      <label
                        className={`block text-xs font-medium mb-0.5 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        Current Age
                      </label>
                      <input
                        type="number"
                        value={currentAge}
                        onChange={(e) =>
                          setCurrentAge(parseInt(e.target.value) || 0)
                        }
                        data-testid="age-input"
                        className={`w-full p-1.5 rounded text-xs font-medium border ${
                          darkMode
                            ? "bg-gray-700 border-gray-500"
                            : "bg-white border-gray-300"
                        }`}
                      />
                    </div>
                    <div>
                      <label
                        className={`block text-xs font-medium mb-0.5 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        Retirement Age
                      </label>
                      <input
                        type="number"
                        value={retirementAge}
                        onChange={(e) =>
                          setRetirementAge(parseInt(e.target.value) || 55)
                        }
                        min={currentAge + 1}
                        className={`w-full p-1.5 rounded text-xs font-medium border ${
                          darkMode
                            ? "bg-gray-700 border-gray-500"
                            : "bg-white border-gray-300"
                        }`}
                      />
                    </div>
                    <div>
                      <label
                        className={`block text-xs font-medium mb-0.5 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        Exp. Return (%pa)
                      </label>
                      <input
                        type="number"
                        step="0.1"
                        value={expectedReturn}
                        onChange={(e) =>
                          setExpectedReturn(parseFloat(e.target.value) || 0)
                        }
                        className={`w-full p-1.5 rounded text-xs font-medium border ${
                          darkMode
                            ? "bg-gray-700 border-gray-500"
                            : "bg-white border-gray-300"
                        }`}
                      />
                    </div>
                    <div>
                      <label
                        className={`block text-xs font-medium mb-0.5 ${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        }`}
                      >
                        Inflation (%pa)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        value={inflationRate}
                        onChange={(e) =>
                          setInflationRate(parseFloat(e.target.value) || 0)
                        }
                        className={`w-full p-1.5 rounded text-xs font-medium border ${
                          darkMode
                            ? "bg-gray-700 border-gray-500"
                            : "bg-white border-gray-300"
                        }`}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === "budget" && (
          <div className="grid lg:grid-cols-2 gap-5 md:gap-6">
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border-red-500/25"
                  : "bg-white/90 border-red-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h2
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-red-400 text-xl">🎯</span>{" "}
                {t("financial:expenses.title")}
              </h2>
              <div className="space-y-2.5 md:space-y-3 max-h-[65vh] overflow-y-auto pr-1.5">
                {expenses.map((exp) => (
                  <div
                    key={exp.id}
                    className={`p-2.5 md:p-3 rounded-lg ${
                      darkMode
                        ? "bg-gray-700/60 hover:bg-gray-700/80"
                        : "bg-gray-50 hover:bg-gray-100"
                    } shadow-sm`}
                  >
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 space-y-1.5">
                        <input
                          type="text"
                          value={exp.category}
                          onChange={(e) =>
                            updateExpense(exp.id, "category", e.target.value)
                          }
                          placeholder="Category"
                          className={`w-full p-1.5 rounded text-xs sm:text-sm font-medium border ${
                            darkMode
                              ? "bg-gray-600 border-gray-500"
                              : "bg-white border-gray-300"
                          } focus:ring-1 focus:ring-red-500/50`}
                        />
                        <div className="flex items-center space-x-1.5">
                          <div className="relative flex-1">
                            <span
                              className={`absolute left-2 top-1/2 -translate-y-1/2 text-2xs font-medium ${
                                darkMode ? "text-gray-400" : "text-gray-500"
                              }`}
                            >
                              CHF
                            </span>
                            <input
                              type="number"
                              value={exp.amount}
                              onChange={(e) =>
                                updateExpense(exp.id, "amount", e.target.value)
                              }
                              placeholder="0"
                              className={`w-full pl-8 pr-2 py-1.5 rounded text-xs sm:text-sm font-semibold border ${
                                darkMode
                                  ? "bg-gray-600 border-gray-500"
                                  : "bg-white border-gray-300"
                              } focus:ring-1 focus:ring-red-500/50`}
                            />
                          </div>
                          <label className="flex items-center text-2xs sm:text-xs cursor-pointer">
                            <input
                              type="checkbox"
                              checked={exp.essential}
                              onChange={(e) =>
                                updateExpense(
                                  exp.id,
                                  "essential",
                                  e.target.checked
                                )
                              }
                              className="w-3.5 h-3.5 text-red-500 rounded focus:ring-red-400 border-gray-400 dark:border-gray-500 mr-1"
                            />
                            <span
                              className={`${
                                darkMode ? "text-gray-300" : "text-gray-700"
                              }`}
                            >
                              Essential
                            </span>
                          </label>
                        </div>
                      </div>
                      <button
                        onClick={() => removeExpense(exp.id)}
                        className={`p-1.5 text-red-500 hover:text-red-700 ${
                          darkMode ? "hover:bg-red-700/20" : "hover:bg-red-100"
                        } rounded-md`}
                      >
                        <span className="text-lg">−</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
              <button
                onClick={addExpense}
                className={`mt-3 md:mt-4 flex items-center w-full justify-center py-2 px-3 rounded-md font-medium text-sm transition-colors transform hover:scale-101 ${
                  darkMode
                    ? "bg-gradient-to-r from-red-600 to-pink-600 text-white shadow-sm"
                    : "bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-sm"
                } hover:shadow-md`}
              >
                <span className="mr-1.5 text-base">+</span> {t("actions.add")}{" "}
                {t("financial:expenses.category")}
              </button>
            </div>
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border-emerald-500/25"
                  : "bg-white/90 border-emerald-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h2
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-emerald-400 text-xl">⭐</span>{" "}
                {t("financial:savings.title")}
              </h2>

              {/* Emergency Fund Target Section */}
              <div
                className={`mb-4 p-3 rounded-lg ${
                  darkMode
                    ? "bg-blue-800/30 border border-blue-600/40"
                    : "bg-blue-50 border border-blue-200"
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3
                    className={`text-sm font-semibold ${
                      darkMode ? "text-blue-300" : "text-blue-700"
                    }`}
                  >
                    🛡️ Emergency Fund Target
                  </h3>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      value={emergencyFundTargetMonths}
                      onChange={(e) =>
                        setEmergencyFundTargetMonths(
                          parseInt(e.target.value) || 3
                        )
                      }
                      min="1"
                      max="12"
                      className={`w-16 p-1 rounded text-xs font-medium border ${
                        darkMode
                          ? "bg-gray-700 border-gray-500 text-white"
                          : "bg-white border-gray-300"
                      }`}
                    />
                    <span
                      className={`text-xs ${
                        darkMode ? "text-blue-300" : "text-blue-700"
                      }`}
                    >
                      months
                    </span>
                  </div>
                </div>
                <div className="space-y-1 text-xs">
                  <div
                    className={`flex justify-between ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    <span>Target Amount:</span>
                    <span className="font-semibold">
                      {formatCurrency(emergencyFundTarget)}
                    </span>
                  </div>
                  <div
                    className={`flex justify-between ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    <span>Monthly Expenses:</span>
                    <span>
                      {formatCurrency(totalExpenses)} ×{" "}
                      {emergencyFundTargetMonths}
                    </span>
                  </div>
                  {emergencyFundReached && (
                    <div
                      className={`mt-2 p-2 rounded text-xs ${
                        darkMode
                          ? "bg-green-800/50 text-green-200 border border-green-600/40"
                          : "bg-green-100 text-green-800 border border-green-300"
                      }`}
                    >
                      ✅ <strong>Target Reached!</strong> Emergency fund
                      contributions ({formatCurrency(emergencyFundMonthly)}
                      /month) will be redirected to{" "}
                      {investmentPortfolioGoal?.goal || "Investment Portfolio"}.
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2.5 md:space-y-3 max-h-[65vh] overflow-y-auto pr-1.5">
                {savings.map((sav) => {
                  const isEmergencyFund = sav.goal
                    .toLowerCase()
                    .includes("emergency");
                  const isInvestmentPortfolio =
                    sav.goal.toLowerCase().includes("investment") ||
                    sav.goal.toLowerCase().includes("portfolio");
                  const effectiveAmount =
                    isEmergencyFund && emergencyFundReached
                      ? 0
                      : parseFloat(sav.amount) || 0;
                  const bonusAmount =
                    isInvestmentPortfolio && emergencyFundReached
                      ? emergencyFundMonthly
                      : 0;
                  const totalAmount = effectiveAmount + bonusAmount;

                  return (
                    <div
                      key={sav.id}
                      className={`p-2.5 md:p-3 rounded-lg ${
                        darkMode
                          ? "bg-gray-700/60 hover:bg-gray-700/80"
                          : "bg-gray-50 hover:bg-gray-100"
                      } shadow-sm ${
                        isEmergencyFund && emergencyFundReached
                          ? "opacity-60"
                          : ""
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 space-y-1.5">
                          <input
                            type="text"
                            value={sav.goal}
                            onChange={(e) =>
                              updateSavingsGoal(sav.id, "goal", e.target.value)
                            }
                            placeholder="Goal"
                            className={`w-full p-1.5 rounded text-xs sm:text-sm font-medium border ${
                              darkMode
                                ? "bg-gray-600 border-gray-500"
                                : "bg-white border-gray-300"
                            } focus:ring-1 focus:ring-emerald-500/50`}
                          />
                          <div className="relative">
                            <span
                              className={`absolute left-2 top-1/2 -translate-y-1/2 text-2xs font-medium ${
                                darkMode ? "text-gray-400" : "text-gray-500"
                              }`}
                            >
                              CHF
                            </span>
                            <input
                              type="number"
                              value={sav.amount}
                              onChange={(e) =>
                                updateSavingsGoal(
                                  sav.id,
                                  "amount",
                                  e.target.value
                                )
                              }
                              placeholder="0"
                              className={`w-full pl-8 pr-2 py-1.5 rounded text-xs sm:text-sm font-semibold border ${
                                darkMode
                                  ? "bg-gray-600 border-gray-500"
                                  : "bg-white border-gray-300"
                              } focus:ring-1 focus:ring-emerald-500/50`}
                            />
                          </div>
                          {sav.goal.toLowerCase().includes("pillar 3a") && (
                            <div
                              className={`text-2xs mt-0.5 ${
                                darkMode
                                  ? "text-emerald-300"
                                  : "text-emerald-600"
                              }`}
                            >
                              🇨🇭 Max: {formatCurrency(pillar3aLimit / 12)}/mo
                            </div>
                          )}
                          {isEmergencyFund && emergencyFundReached && (
                            <div
                              className={`text-2xs mt-0.5 ${
                                darkMode ? "text-orange-300" : "text-orange-600"
                              }`}
                            >
                              🎯 Target reached! Redirecting{" "}
                              {formatCurrency(emergencyFundMonthly)} to
                              investments
                            </div>
                          )}
                          {isInvestmentPortfolio &&
                            emergencyFundReached &&
                            bonusAmount > 0 && (
                              <div
                                className={`text-2xs mt-0.5 ${
                                  darkMode ? "text-green-300" : "text-green-600"
                                }`}
                              >
                                💰 Total with emergency fund redirect:{" "}
                                {formatCurrency(totalAmount)}
                              </div>
                            )}
                        </div>
                        <button
                          onClick={() => removeSavingsGoal(sav.id)}
                          className={`p-1.5 text-red-500 hover:text-red-700 ${
                            darkMode
                              ? "hover:bg-red-700/20"
                              : "hover:bg-red-100"
                          } rounded-md`}
                        >
                          <span className="text-lg">−</span>
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
              <button
                onClick={addSavingsGoal}
                className={`mt-3 md:mt-4 flex items-center w-full justify-center py-2 px-3 rounded-md font-medium text-sm transition-colors transform hover:scale-101 ${
                  darkMode
                    ? "bg-gradient-to-r from-emerald-600 to-teal-600 text-white shadow-sm"
                    : "bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-sm"
                } hover:shadow-md`}
              >
                <span className="mr-1.5 text-base">+</span> Add Savings Goal
              </button>
            </div>
          </div>
        )}

        {activeTab === "target" && (
          <div className="space-y-5 md:space-y-6">
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border-purple-500/25"
                  : "bg-white/90 border-purple-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-purple-400 text-xl">🎯</span>{" "}
                Retirement & Savings Configuration
              </h3>
              <div className="grid md:grid-cols-2 gap-3 md:gap-4 mb-3 md:mb-4">
                <div>
                  <label
                    className={`block text-xs sm:text-sm font-medium mb-1 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    } flex items-center`}
                  >
                    <span className="mr-1 text-green-400 text-sm">💎</span>{" "}
                    Target Monthly Retirement Income
                  </label>
                  <div className="relative">
                    <span
                      className={`absolute left-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                        darkMode ? "text-gray-400" : "text-gray-500"
                      }`}
                    >
                      CHF
                    </span>
                    <input
                      type="number"
                      value={targetRetirementIncome}
                      onChange={(e) =>
                        setTargetRetirementIncome(e.target.value)
                      }
                      className={`w-full pl-10 pr-3 py-2 rounded-md text-sm md:text-base font-semibold border ${
                        darkMode
                          ? "bg-gray-700 border-gray-600 text-white focus:border-purple-400"
                          : "bg-gray-50 border-gray-300 text-gray-800 focus:border-purple-500"
                      } transition-colors focus:ring-1 focus:ring-purple-500/30`}
                    />
                  </div>
                </div>
                <div>
                  <label
                    className={`block text-xs sm:text-sm font-medium mb-1 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    } flex items-center`}
                  >
                    <span className="mr-1 text-teal-400 text-sm">%</span> Target
                    Overall Savings Rate
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      step="0.1"
                      value={targetOverallSavingsRate}
                      onChange={(e) =>
                        setTargetOverallSavingsRate(
                          parseFloat(e.target.value) || 0
                        )
                      }
                      className={`w-full pl-3 pr-8 py-2 rounded-md text-sm md:text-base font-semibold border ${
                        darkMode
                          ? "bg-gray-700 border-gray-600 text-white focus:border-teal-400"
                          : "bg-gray-50 border-gray-300 text-gray-800 focus:border-teal-500"
                      } transition-colors focus:ring-1 focus:ring-teal-500/30`}
                    />
                    <span
                      className={`absolute right-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                        darkMode ? "text-gray-400" : "text-gray-500"
                      }`}
                    >
                      %
                    </span>
                  </div>
                </div>
              </div>
              <div className="grid md:grid-cols-2 gap-3 md:gap-4 mb-3 md:mb-4">
                <div>
                  <label
                    className={`block text-xs sm:text-sm font-medium mb-1 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Current Total Savings (Portfolio Start)
                  </label>
                  <div className="relative">
                    <span
                      className={`absolute left-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                        darkMode ? "text-gray-400" : "text-gray-500"
                      }`}
                    >
                      CHF
                    </span>
                    <input
                      type="number"
                      value={currentSavings}
                      onChange={(e) => setCurrentSavings(e.target.value)}
                      className={`w-full pl-10 pr-3 py-2 rounded-md text-sm md:text-base font-semibold border ${
                        darkMode
                          ? "bg-gray-700 border-gray-600 text-white focus:border-indigo-400"
                          : "bg-gray-50 border-gray-300 text-gray-800 focus:border-indigo-500"
                      } transition-colors focus:ring-1 focus:ring-indigo-500/30`}
                    />
                  </div>
                </div>
                <div>
                  <label
                    className={`block text-xs sm:text-sm font-medium mb-1 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    } flex items-center`}
                  >
                    <span className="mr-1 text-blue-400 text-sm">🏛️</span>{" "}
                    Current Pension Leaving Benefits
                  </label>
                  <div className="relative">
                    <span
                      className={`absolute left-2.5 top-1/2 -translate-y-1/2 text-xs font-medium ${
                        darkMode ? "text-gray-400" : "text-gray-500"
                      }`}
                    >
                      CHF
                    </span>
                    <input
                      type="number"
                      value={currentPensionLeavingBenefits}
                      onChange={(e) =>
                        setCurrentPensionLeavingBenefits(e.target.value)
                      }
                      className={`w-full pl-10 pr-3 py-2 rounded-md text-sm md:text-base font-semibold border ${
                        darkMode
                          ? "bg-gray-700 border-gray-600 text-white focus:border-blue-400"
                          : "bg-gray-50 border-gray-300 text-gray-800 focus:border-blue-500"
                      } transition-colors focus:ring-1 focus:ring-blue-500/30`}
                    />
                  </div>
                  <div
                    className={`text-2xs mt-0.5 ${
                      darkMode ? "text-blue-300" : "text-blue-600"
                    }`}
                  >
                    Contribution: {formatCurrency(adjustedPensionContribution)}
                    /mo ({incomePercentage}% of{" "}
                    {formatCurrency(basePensionContribution)})
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 mb-3 md:mb-4">
                <div>
                  <label
                    className={`block text-xs font-medium mb-0.5 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Current Age
                  </label>
                  <input
                    type="number"
                    value={currentAge}
                    onChange={(e) =>
                      setCurrentAge(parseInt(e.target.value) || 0)
                    }
                    data-testid="age-input"
                    className={`w-full p-1.5 rounded text-xs font-medium border ${
                      darkMode
                        ? "bg-gray-700 border-gray-500"
                        : "bg-white border-gray-300"
                    }`}
                  />
                </div>
                <div>
                  <label
                    className={`block text-xs font-medium mb-0.5 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Retirement Age
                  </label>
                  <input
                    type="number"
                    value={retirementAge}
                    onChange={(e) =>
                      setRetirementAge(parseInt(e.target.value) || 55)
                    }
                    min={currentAge + 1}
                    className={`w-full p-1.5 rounded text-xs font-medium border ${
                      darkMode
                        ? "bg-gray-700 border-gray-500"
                        : "bg-white border-gray-300"
                    }`}
                  />
                </div>
                <div>
                  <label
                    className={`block text-xs font-medium mb-0.5 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Exp. Return (%pa)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={expectedReturn}
                    onChange={(e) =>
                      setExpectedReturn(parseFloat(e.target.value) || 0)
                    }
                    className={`w-full p-1.5 rounded text-xs font-medium border ${
                      darkMode
                        ? "bg-gray-700 border-gray-500"
                        : "bg-white border-gray-300"
                    }`}
                  />
                </div>
                <div>
                  <label
                    className={`block text-xs font-medium mb-0.5 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Inflation (%pa)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={inflationRate}
                    onChange={(e) =>
                      setInflationRate(parseFloat(e.target.value) || 0)
                    }
                    className={`w-full p-1.5 rounded text-xs font-medium border ${
                      darkMode
                        ? "bg-gray-700 border-gray-500"
                        : "bg-white border-gray-300"
                    }`}
                  />
                </div>
              </div>
              <button
                onClick={() => setShowTargetAnalysis(!showTargetAnalysis)}
                className={`mt-4 w-full flex items-center justify-center py-2.5 px-4 rounded-md font-medium text-sm transition-colors transform hover:scale-101 ${
                  darkMode
                    ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-sm"
                    : "bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-sm"
                } hover:shadow-md`}
              >
                <span className="mr-1.5 text-base">🧠</span>
                {showTargetAnalysis ? "Hide" : "Analyze"} Target Plan
              </button>
            </div>
            {showTargetAnalysis && targetAnalysis && (
              <div
                className={`p-5 md:p-6 rounded-xl ${
                  darkMode
                    ? "bg-gray-800/70 border-indigo-500/25"
                    : "bg-white/90 border-indigo-200 shadow-md"
                } backdrop-blur-sm space-y-3 md:space-y-4`}
              >
                <h4
                  className={`text-lg md:text-xl font-bold ${
                    darkMode ? "text-white" : "text-gray-800"
                  } flex items-center`}
                >
                  <span className="mr-1.5 text-indigo-400 text-lg">📊</span>
                  Analysis Results
                </h4>
                <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-2.5 md:gap-3">
                  {[
                    {
                      label: "Req. Monthly Savings",
                      value: targetAnalysis.requiredMonthlySavings,
                      color: "blue",
                    },
                    {
                      label: "Current Fixed Savings",
                      value: targetAnalysis.currentSavings,
                      color: "emerald",
                    },
                    {
                      label: "Monthly Gap (Fixed Goals)",
                      value: targetAnalysis.gap,
                      type: targetAnalysis.gap <= 0 ? "surplus" : "deficit",
                      color: targetAnalysis.gap <= 0 ? "green" : "red",
                    },
                    {
                      label: "Years to Retirement",
                      value: targetAnalysis.yearsToRetirement,
                      color: "purple",
                    },
                  ].map((item) => (
                    <div
                      key={item.label}
                      className={`p-2.5 rounded-md ${
                        darkMode
                          ? `bg-gray-700/70 border-gray-600`
                          : `bg-gray-100 border-gray-200`
                      } border`}
                    >
                      <div
                        className={`text-2xs font-medium mb-0.5 ${
                          darkMode
                            ? `text-${item.color}-300`
                            : `text-${item.color}-700`
                        }`}
                      >
                        {item.label}
                      </div>
                      <div
                        className={`text-sm md:text-base font-bold ${
                          darkMode
                            ? `text-${item.color}-400`
                            : `text-${item.color}-600`
                        }`}
                      >
                        {item.type === "surplus"
                          ? "✅ "
                          : item.type === "deficit" && item.value > 0
                          ? "⚠️ "
                          : ""}
                        {item.label === "Years to Retirement"
                          ? item.value
                          : formatCurrency(
                              item.label.includes("Gap")
                                ? Math.abs(item.value)
                                : item.value
                            )}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="space-y-2.5">
                  <h5
                    className={`text-sm sm:text-base font-semibold ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Action Plan:
                  </h5>
                  {targetAnalysis.recommendations.map((rec, idx) => (
                    <div
                      key={idx}
                      className={`p-2.5 rounded-md flex items-start space-x-1.5 text-xs ${
                        rec.type === "success"
                          ? darkMode
                            ? "bg-green-800/50 border-green-600/60"
                            : "bg-green-50 border-green-200"
                          : rec.type === "warning"
                          ? darkMode
                            ? "bg-yellow-800/50 border-yellow-600/60"
                            : "bg-yellow-50 border-yellow-200"
                          : rec.type === "error"
                          ? darkMode
                            ? "bg-red-800/50 border-red-600/60"
                            : "bg-red-50 border-red-200"
                          : darkMode
                          ? "bg-blue-800/50 border-blue-600/60"
                          : "bg-blue-50 border-blue-200"
                      } border`}
                    >
                      <span className="text-base mt-0.5">{rec.icon}</span>
                      <div>
                        <strong
                          className={`font-semibold ${
                            rec.type === "success"
                              ? darkMode
                                ? "text-green-300"
                                : "text-green-700"
                              : rec.type === "warning"
                              ? darkMode
                                ? "text-yellow-300"
                                : "text-yellow-700"
                              : rec.type === "error"
                              ? darkMode
                                ? "text-red-300"
                                : "text-red-700"
                              : darkMode
                              ? "text-blue-300"
                              : "text-blue-700"
                          }`}
                        >
                          {rec.title}
                        </strong>
                        <p
                          className={`${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          } text-2xs`}
                        >
                          {rec.description}
                        </p>
                        <p
                          className={`mt-0.5 text-2xs font-medium ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          💡 {rec.action}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === "companyIncome" && (
          <div className="space-y-5 md:space-y-6">
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border-purple-500/25"
                  : "bg-white/90 border-purple-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-purple-400 text-xl">🏢</span> Company
                Income Progression
              </h3>

              <div className="grid md:grid-cols-3 gap-3 md:gap-4 mb-4">
                <div
                  className={`p-3 rounded-lg ${
                    darkMode
                      ? "bg-green-800/30 border border-green-600/40"
                      : "bg-green-50 border border-green-200"
                  }`}
                >
                  <div
                    className={`text-xs font-medium mb-1 ${
                      darkMode ? "text-green-300" : "text-green-700"
                    }`}
                  >
                    Starting Income
                  </div>
                  <div
                    className={`text-lg font-bold ${
                      darkMode ? "text-green-400" : "text-green-600"
                    }`}
                  >
                    {formatCurrency(parseFloat(companyIncome) || 0)}/mo
                  </div>
                  <div
                    className={`text-2xs ${
                      darkMode ? "text-green-200" : "text-green-600"
                    }`}
                  >
                    From {companyIncomeStartYear} (Age {companyIncomeStartAge})
                  </div>
                </div>
                <div
                  className={`p-3 rounded-lg ${
                    darkMode
                      ? "bg-blue-800/30 border border-blue-600/40"
                      : "bg-blue-50 border border-blue-200"
                  }`}
                >
                  <div
                    className={`text-xs font-medium mb-1 ${
                      darkMode ? "text-blue-300" : "text-blue-700"
                    }`}
                  >
                    Annual Growth
                  </div>
                  <div
                    className={`text-lg font-bold ${
                      darkMode ? "text-blue-400" : "text-blue-600"
                    }`}
                  >
                    {companyIncomeGrowthRate}%
                  </div>
                  <div
                    className={`text-2xs ${
                      darkMode ? "text-blue-200" : "text-blue-600"
                    }`}
                  >
                    Compounding yearly
                  </div>
                </div>
                <div
                  className={`p-3 rounded-lg ${
                    darkMode
                      ? "bg-purple-800/30 border border-purple-600/40"
                      : "bg-purple-50 border border-purple-200"
                  }`}
                >
                  <div
                    className={`text-xs font-medium mb-1 ${
                      darkMode ? "text-purple-300" : "text-purple-700"
                    }`}
                  >
                    Total Lifetime Earnings
                  </div>
                  <div
                    className={`text-lg font-bold ${
                      darkMode ? "text-purple-400" : "text-purple-600"
                    }`}
                  >
                    {formatCurrency(totalCompanyEarnings)}
                  </div>
                  <div
                    className={`text-2xs ${
                      darkMode ? "text-purple-200" : "text-purple-600"
                    }`}
                  >
                    Until retirement at {retirementAge}
                  </div>
                </div>
              </div>

              {companyIncomeProgression.length > 0 && (
                <div className="space-y-3">
                  <h4
                    className={`text-base font-semibold ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Year-by-Year Progression
                  </h4>
                  <div className="max-h-[60vh] overflow-y-auto pr-1.5">
                    <table className="w-full text-left text-xs sm:text-sm">
                      <thead
                        className={`sticky top-0 ${
                          darkMode ? "bg-gray-700/90" : "bg-gray-100/90"
                        } backdrop-blur-sm z-10`}
                      >
                        <tr>
                          {[
                            "Year",
                            "Age",
                            "Monthly",
                            "Annual",
                            "Cumulative",
                            "Status",
                          ].map((h) => (
                            <th
                              key={h}
                              className={`py-1.5 px-1 md:px-2 font-semibold ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              {h}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody
                        className={
                          darkMode
                            ? "divide-y divide-gray-700"
                            : "divide-y divide-gray-200"
                        }
                      >
                        {companyIncomeProgression.map((entry) => (
                          <tr
                            key={entry.year}
                            className={`${
                              darkMode
                                ? "hover:bg-gray-700/50"
                                : "hover:bg-gray-50/50"
                            } ${
                              entry.isActive
                                ? darkMode
                                  ? "bg-green-900/20"
                                  : "bg-green-50/50"
                                : "opacity-60"
                            }`}
                          >
                            <td className="py-1.5 px-1 md:px-2">
                              {entry.year}
                            </td>
                            <td className="py-1.5 px-1 md:px-2">{entry.age}</td>
                            <td className="py-1.5 px-1 md:px-2">
                              {entry.isActive
                                ? formatCurrency(entry.monthlyIncome)
                                : "-"}
                            </td>
                            <td className="py-1.5 px-1 md:px-2 font-medium">
                              {entry.isActive
                                ? formatCurrency(entry.yearlyIncome)
                                : "-"}
                            </td>
                            <td className="py-1.5 px-1 md:px-2">
                              {formatCurrency(entry.cumulativeEarnings)}
                            </td>
                            <td className="py-1.5 px-1 md:px-2">
                              {entry.year < companyIncomeStartYear
                                ? "⏳ Waiting"
                                : entry.age >= retirementAge
                                ? "🏖️ Retired"
                                : entry.isActive
                                ? "💼 Active"
                                : "❌ Inactive"}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {totalCompanyEarnings > 0 && (
                    <div
                      className={`mt-4 p-3 rounded-lg ${
                        darkMode
                          ? "bg-gradient-to-r from-purple-800/60 to-blue-800/60 border border-purple-500/30"
                          : "bg-gradient-to-r from-purple-100 to-blue-100 border border-purple-300"
                      }`}
                    >
                      <div className="flex flex-col sm:flex-row items-center justify-between">
                        <h4
                          className={`text-base font-bold ${
                            darkMode ? "text-purple-300" : "text-purple-700"
                          }`}
                        >
                          🚀 Company Income Impact
                        </h4>
                        <div className="text-right mt-1 sm:mt-0">
                          <div
                            className={`text-xl font-black ${
                              darkMode ? "text-purple-400" : "text-purple-600"
                            }`}
                          >
                            {formatCurrency(totalCompanyEarnings)}
                          </div>
                          <div
                            className={`text-2xs ${
                              darkMode ? "text-purple-300" : "text-purple-700"
                            }`}
                          >
                            Total over{" "}
                            {Math.max(0, retirementAge - companyIncomeStartAge)}{" "}
                            years
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {companyIncomeProgression.length === 0 && (
                <div
                  className={`text-center py-8 ${
                    darkMode ? "text-gray-400" : "text-gray-600"
                  }`}
                >
                  <span className="text-4xl mb-2 block">🏢</span>
                  <p>Set a company income amount to see progression</p>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === "taxOptimization" && (
          <div className="space-y-5 md:space-y-6">
            {/* Swiss Tax Configuration */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-red-500/25"
                  : "bg-white/90 border border-red-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-red-500 text-xl">🇨🇭</span>{" "}
                {t("swiss:taxes.title")}
              </h3>

              <div className="grid md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Canton
                  </label>
                  <select
                    value={selectedCanton}
                    onChange={(e) => setSelectedCanton(e.target.value)}
                    data-testid="canton-select"
                    className={`w-full p-3 rounded-lg border ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-white border-gray-300 text-gray-800"
                    } focus:ring-2 focus:ring-red-500`}
                  >
                    {Object.entries(SwissTaxEngine.cantons).map(
                      ([code, canton]) => (
                        <option key={code} value={code}>
                          {canton.name} ({code})
                        </option>
                      )
                    )}
                  </select>
                </div>

                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Civil Status
                  </label>
                  <select
                    value={civilStatus}
                    onChange={(e) => setCivilStatus(e.target.value)}
                    data-testid="civil-status-select"
                    className={`w-full p-3 rounded-lg border ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-white border-gray-300 text-gray-800"
                    } focus:ring-2 focus:ring-red-500`}
                  >
                    <option value="single">Single</option>
                    <option value="married">Married</option>
                  </select>
                </div>

                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Pension Fund (BVG)
                  </label>
                  <select
                    value={hasSecondPillar ? "yes" : "no"}
                    onChange={(e) =>
                      setHasSecondPillar(e.target.value === "yes")
                    }
                    className={`w-full p-3 rounded-lg border ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-white border-gray-300 text-gray-800"
                    } focus:ring-2 focus:ring-red-500`}
                  >
                    <option value="yes">Yes (Employee)</option>
                    <option value="no">No (Self-employed)</option>
                  </select>
                </div>
              </div>

              <div>
                <label
                  className={`block text-sm font-medium mb-2 ${
                    darkMode ? "text-gray-300" : "text-gray-700"
                  }`}
                >
                  Current Pillar 3a Balance
                </label>
                <div className="relative">
                  <span
                    className={`absolute left-3 top-1/2 -translate-y-1/2 text-sm font-medium ${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  >
                    CHF
                  </span>
                  <input
                    type="number"
                    value={currentPillar3a}
                    onChange={(e) => setCurrentPillar3a(e.target.value)}
                    className={`w-full pl-12 pr-3 py-3 rounded-lg border ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-white border-gray-300 text-gray-800"
                    } focus:ring-2 focus:ring-red-500`}
                    placeholder="0"
                  />
                </div>
              </div>
            </div>

            {/* Current Tax Situation */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-blue-500/25"
                  : "bg-white/90 border border-blue-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-blue-500 text-xl">📊</span> Current
                Tax Situation
              </h3>

              <div className="grid md:grid-cols-2 gap-4">
                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Annual Tax Breakdown
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Federal Tax:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-red-400" : "text-red-600"
                        }`}
                      >
                        {formatCurrency(
                          swissTaxAnalysis.currentTaxSituation.federalTax
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Cantonal Tax:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-red-400" : "text-red-600"
                        }`}
                      >
                        {formatCurrency(
                          swissTaxAnalysis.currentTaxSituation.cantonalTax
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Wealth Tax:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-red-400" : "text-red-600"
                        }`}
                      >
                        {formatCurrency(
                          swissTaxAnalysis.currentTaxSituation.wealthTax
                        )}
                      </span>
                    </div>
                    <div className="border-t pt-2 mt-2">
                      <div className="flex justify-between">
                        <span
                          className={`text-base font-semibold ${
                            darkMode ? "text-white" : "text-gray-800"
                          }`}
                        >
                          Total Tax (Annual):
                        </span>
                        <span
                          className={`text-base font-bold ${
                            darkMode ? "text-red-300" : "text-red-700"
                          }`}
                        >
                          {formatCurrency(
                            swissTaxAnalysis.currentTaxSituation.totalTax
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between mt-1">
                        <span
                          className={`text-sm ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          Monthly Tax:
                        </span>
                        <span
                          data-testid="monthly-tax"
                          className={`text-sm font-medium ${
                            darkMode ? "text-red-400" : "text-red-600"
                          }`}
                        >
                          {formatCurrency(
                            swissTaxAnalysis.currentTaxSituation.totalTax / 12
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Tax Rates
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Effective Rate:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        {swissTaxAnalysis.currentTaxSituation.effectiveRate.toFixed(
                          1
                        )}
                        %
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Marginal Rate:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        {swissTaxAnalysis.currentTaxSituation.marginalRate.toFixed(
                          1
                        )}
                        %
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Canton:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-green-400" : "text-green-600"
                        }`}
                      >
                        {SwissTaxEngine.cantons[selectedCanton].name}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Net Income:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-green-400" : "text-green-600"
                        }`}
                      >
                        {formatCurrency(
                          totalMonthlyIncome * 12 -
                            swissTaxAnalysis.currentTaxSituation.totalTax
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Tax Optimization Recommendations */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-green-500/25"
                  : "bg-white/90 border border-green-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-green-500 text-xl">💡</span> Tax
                Optimization Recommendations
              </h3>

              {swissTaxAnalysis.taxOptimizations.length > 0 ? (
                <div className="space-y-4">
                  {swissTaxAnalysis.taxOptimizations.map(
                    (optimization, index) => (
                      <div
                        key={index}
                        className={`p-4 rounded-lg border-l-4 ${
                          optimization.priority === "high"
                            ? darkMode
                              ? "bg-red-800/30 border-red-500"
                              : "bg-red-50 border-red-500"
                            : optimization.priority === "medium"
                            ? darkMode
                              ? "bg-yellow-800/30 border-yellow-500"
                              : "bg-yellow-50 border-yellow-500"
                            : darkMode
                            ? "bg-green-800/30 border-green-500"
                            : "bg-green-50 border-green-500"
                        }`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h4
                            className={`text-lg font-semibold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            {optimization.title}
                          </h4>
                          <span
                            className={`px-2 py-1 rounded text-xs font-medium ${
                              optimization.priority === "high"
                                ? "bg-red-500 text-white"
                                : optimization.priority === "medium"
                                ? "bg-yellow-500 text-white"
                                : "bg-green-500 text-white"
                            }`}
                          >
                            {optimization.priority.toUpperCase()}
                          </span>
                        </div>
                        <p
                          className={`text-sm mb-3 ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          {optimization.description}
                        </p>
                        <div className="grid md:grid-cols-2 gap-3">
                          <div>
                            <span
                              className={`text-xs font-medium ${
                                darkMode ? "text-gray-400" : "text-gray-500"
                              }`}
                            >
                              Annual Savings:
                            </span>
                            <div
                              className={`text-lg font-bold ${
                                darkMode ? "text-green-400" : "text-green-600"
                              }`}
                            >
                              {formatCurrency(optimization.annualSavings)}
                            </div>
                          </div>
                          <div>
                            <span
                              className={`text-xs font-medium ${
                                darkMode ? "text-gray-400" : "text-gray-500"
                              }`}
                            >
                              Lifetime Savings:
                            </span>
                            <div
                              className={`text-lg font-bold ${
                                darkMode ? "text-green-400" : "text-green-600"
                              }`}
                            >
                              {formatCurrency(optimization.lifetimeSavings)}
                            </div>
                          </div>
                        </div>
                        <div
                          className={`mt-3 p-3 rounded ${
                            darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                          }`}
                        >
                          <span
                            className={`text-xs font-medium ${
                              darkMode ? "text-gray-400" : "text-gray-500"
                            }`}
                          >
                            Implementation:
                          </span>
                          <p
                            className={`text-sm mt-1 ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            {optimization.implementation}
                          </p>
                        </div>
                      </div>
                    )
                  )}
                </div>
              ) : (
                <div
                  className={`text-center py-8 ${
                    darkMode ? "text-gray-400" : "text-gray-500"
                  }`}
                >
                  <div className="text-4xl mb-3">🎯</div>
                  <p>Your tax situation is already well optimized!</p>
                  <p className="text-sm mt-2">
                    Consider reviewing when your income or circumstances change.
                  </p>
                </div>
              )}
            </div>

            {/* Cantonal Tax Comparison */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-purple-500/25"
                  : "bg-white/90 border border-purple-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-purple-500 text-xl">🗺️</span>{" "}
                Cantonal Tax Comparison
              </h3>

              <div className="mb-4">
                <p
                  className={`text-sm ${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  }`}
                >
                  Compare your tax burden across all 26 Swiss cantons. Based on
                  your current income of{" "}
                  {formatCurrency(totalMonthlyIncome * 12)} annually.
                </p>
              </div>

              <div className="grid gap-3 max-h-96 overflow-y-auto">
                {swissTaxAnalysis.cantonalComparison
                  .slice(0, 10)
                  .map((canton, index) => (
                    <div
                      key={canton.canton}
                      className={`p-3 rounded-lg border ${
                        canton.canton === selectedCanton
                          ? darkMode
                            ? "bg-blue-800/30 border-blue-500"
                            : "bg-blue-50 border-blue-500"
                          : darkMode
                          ? "bg-gray-700/60 border-gray-600"
                          : "bg-gray-100/80 border-gray-200"
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex items-center">
                          <span
                            className={`text-lg font-bold mr-3 ${
                              index < 3
                                ? "text-green-500"
                                : index < 6
                                ? "text-yellow-500"
                                : "text-red-500"
                            }`}
                          >
                            #{index + 1}
                          </span>
                          <div>
                            <h4
                              className={`font-semibold ${
                                darkMode ? "text-white" : "text-gray-800"
                              }`}
                            >
                              {canton.name} ({canton.canton})
                              {canton.canton === selectedCanton && (
                                <span
                                  className={`ml-2 px-2 py-0.5 rounded text-xs ${
                                    darkMode
                                      ? "bg-blue-600 text-white"
                                      : "bg-blue-500 text-white"
                                  }`}
                                >
                                  Current
                                </span>
                              )}
                            </h4>
                            <div className="flex space-x-4 text-sm">
                              <span
                                className={`${
                                  darkMode ? "text-gray-300" : "text-gray-600"
                                }`}
                              >
                                Effective Rate:{" "}
                                {canton.effectiveRate.toFixed(1)}%
                              </span>
                              <span
                                className={`${
                                  darkMode ? "text-gray-300" : "text-gray-600"
                                }`}
                              >
                                Wealth Tax: {formatCurrency(canton.wealthTax)}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div
                            className={`text-lg font-bold ${
                              darkMode ? "text-red-400" : "text-red-600"
                            }`}
                          >
                            {formatCurrency(canton.totalTax)}
                          </div>
                          {canton.savings > 0 && (
                            <div
                              className={`text-sm font-medium ${
                                darkMode ? "text-green-400" : "text-green-600"
                              }`}
                            >
                              Save {formatCurrency(canton.savings)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
              </div>

              <div className="mt-4 text-center">
                <button
                  onClick={() => {
                    const element = document.querySelector(
                      ".cantonal-comparison-full"
                    );
                    if (element) {
                      element.classList.toggle("hidden");
                    }
                  }}
                  className={`px-4 py-2 rounded-lg text-sm font-medium ${
                    darkMode
                      ? "bg-purple-600 text-white hover:bg-purple-700"
                      : "bg-purple-500 text-white hover:bg-purple-600"
                  } transition-colors`}
                >
                  Show All 26 Cantons
                </button>
              </div>

              <div className="cantonal-comparison-full hidden mt-4 grid gap-2 max-h-64 overflow-y-auto">
                {swissTaxAnalysis.cantonalComparison
                  .slice(10)
                  .map((canton, index) => (
                    <div
                      key={canton.canton}
                      className={`p-2 rounded border ${
                        darkMode
                          ? "bg-gray-700/40 border-gray-600"
                          : "bg-gray-50 border-gray-200"
                      }`}
                    >
                      <div className="flex justify-between items-center text-sm">
                        <span
                          className={`font-medium ${
                            darkMode ? "text-gray-300" : "text-gray-700"
                          }`}
                        >
                          #{index + 11} {canton.name} ({canton.canton})
                        </span>
                        <div className="flex space-x-3">
                          <span
                            className={`${
                              darkMode ? "text-gray-400" : "text-gray-600"
                            }`}
                          >
                            {canton.effectiveRate.toFixed(1)}%
                          </span>
                          <span
                            className={`font-medium ${
                              darkMode ? "text-red-400" : "text-red-600"
                            }`}
                          >
                            {formatCurrency(canton.totalTax)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>

            {/* Pillar 3a Deep Dive */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-orange-500/25"
                  : "bg-white/90 border border-orange-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-orange-500 text-xl">🏛️</span> Pillar
                3a Optimization Analysis
              </h3>

              <div className="grid md:grid-cols-2 gap-4">
                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Current Situation
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Annual Limit:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        {formatCurrency(
                          swissTaxAnalysis.pillar3aOptimization
                            .recommendedContribution
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Current Balance:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-green-400" : "text-green-600"
                        }`}
                      >
                        {formatCurrency(parseFloat(currentPillar3a) || 0)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Employment Type:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-purple-400" : "text-purple-600"
                        }`}
                      >
                        {hasSecondPillar ? "Employee (BVG)" : "Self-employed"}
                      </span>
                    </div>
                  </div>
                </div>

                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Optimization Potential
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Annual Tax Savings:
                      </span>
                      <span
                        className={`text-sm font-bold ${
                          darkMode ? "text-green-400" : "text-green-600"
                        }`}
                      >
                        {formatCurrency(
                          swissTaxAnalysis.pillar3aOptimization.annualTaxSavings
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Lifetime Savings:
                      </span>
                      <span
                        className={`text-sm font-bold ${
                          darkMode ? "text-green-400" : "text-green-600"
                        }`}
                      >
                        {formatCurrency(
                          swissTaxAnalysis.pillar3aOptimization
                            .lifetimeTaxSavings
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Withdrawal Accounts:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        {
                          swissTaxAnalysis.pillar3aOptimization
                            .withdrawalStrategy.numberOfAccounts
                        }{" "}
                        recommended
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div
                className={`mt-4 p-4 rounded-lg ${
                  darkMode
                    ? "bg-blue-800/30 border border-blue-600/40"
                    : "bg-blue-50 border border-blue-200"
                }`}
              >
                <h5
                  className={`font-semibold mb-2 ${
                    darkMode ? "text-blue-300" : "text-blue-700"
                  }`}
                >
                  💡 Withdrawal Strategy Recommendation
                </h5>
                <p
                  className={`text-sm ${
                    darkMode ? "text-blue-200" : "text-blue-600"
                  }`}
                >
                  Open{" "}
                  {
                    swissTaxAnalysis.pillar3aOptimization.withdrawalStrategy
                      .numberOfAccounts
                  }{" "}
                  separate Pillar 3a accounts to enable staggered withdrawals
                  over{" "}
                  {
                    swissTaxAnalysis.pillar3aOptimization.withdrawalStrategy
                      .withdrawalYears
                  }{" "}
                  years. This strategy can significantly reduce your withdrawal
                  tax burden by spreading the income across multiple tax years.
                </p>
                <div
                  className={`mt-2 text-xs ${
                    darkMode ? "text-blue-300" : "text-blue-500"
                  }`}
                >
                  Estimated withdrawal tax:{" "}
                  {formatCurrency(
                    swissTaxAnalysis.pillar3aOptimization.withdrawalStrategy
                      .estimatedWithdrawalTax
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "economicData" && (
          <div className="space-y-5 md:space-y-6">
            {/* Economic Regime & Market Sentiment */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-blue-500/25"
                  : "bg-white/90 border border-blue-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-blue-500 text-xl">🌍</span> Swiss
                Economic Overview
              </h3>

              <div className="grid md:grid-cols-2 gap-4 mb-4">
                {/* Economic Regime */}
                <div
                  className={`p-4 rounded-lg ${
                    SwissEconomicDataEngine.detectEconomicRegime().color ===
                    "green"
                      ? darkMode
                        ? "bg-green-800/30 border border-green-600/40"
                        : "bg-green-50 border border-green-200"
                      : SwissEconomicDataEngine.detectEconomicRegime().color ===
                        "red"
                      ? darkMode
                        ? "bg-red-800/30 border border-red-600/40"
                        : "bg-red-50 border border-red-200"
                      : SwissEconomicDataEngine.detectEconomicRegime().color ===
                        "orange"
                      ? darkMode
                        ? "bg-orange-800/30 border border-orange-600/40"
                        : "bg-orange-50 border border-orange-200"
                      : SwissEconomicDataEngine.detectEconomicRegime().color ===
                        "yellow"
                      ? darkMode
                        ? "bg-yellow-800/30 border border-yellow-600/40"
                        : "bg-yellow-50 border border-yellow-200"
                      : darkMode
                      ? "bg-blue-800/30 border border-blue-600/40"
                      : "bg-blue-50 border border-blue-200"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-2 ${
                      SwissEconomicDataEngine.detectEconomicRegime().color ===
                      "green"
                        ? darkMode
                          ? "text-green-300"
                          : "text-green-700"
                        : SwissEconomicDataEngine.detectEconomicRegime()
                            .color === "red"
                        ? darkMode
                          ? "text-red-300"
                          : "text-red-700"
                        : SwissEconomicDataEngine.detectEconomicRegime()
                            .color === "orange"
                        ? darkMode
                          ? "text-orange-300"
                          : "text-orange-700"
                        : SwissEconomicDataEngine.detectEconomicRegime()
                            .color === "yellow"
                        ? darkMode
                          ? "text-yellow-300"
                          : "text-yellow-700"
                        : darkMode
                        ? "text-blue-300"
                        : "text-blue-700"
                    }`}
                  >
                    {SwissEconomicDataEngine.detectEconomicRegime().icon}{" "}
                    Economic Regime
                  </h4>
                  <div
                    className={`text-xl font-bold mb-1 ${
                      SwissEconomicDataEngine.detectEconomicRegime().color ===
                      "green"
                        ? darkMode
                          ? "text-green-400"
                          : "text-green-600"
                        : SwissEconomicDataEngine.detectEconomicRegime()
                            .color === "red"
                        ? darkMode
                          ? "text-red-400"
                          : "text-red-600"
                        : SwissEconomicDataEngine.detectEconomicRegime()
                            .color === "orange"
                        ? darkMode
                          ? "text-orange-400"
                          : "text-orange-600"
                        : SwissEconomicDataEngine.detectEconomicRegime()
                            .color === "yellow"
                        ? darkMode
                          ? "text-yellow-400"
                          : "text-yellow-600"
                        : darkMode
                        ? "text-blue-400"
                        : "text-blue-600"
                    }`}
                  >
                    {SwissEconomicDataEngine.detectEconomicRegime().regime.toUpperCase()}
                  </div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    {SwissEconomicDataEngine.detectEconomicRegime().description}
                  </p>
                  <div
                    className={`mt-2 text-xs ${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  >
                    Confidence:{" "}
                    {SwissEconomicDataEngine.detectEconomicRegime().confidence}
                  </div>
                </div>

                {/* Market Sentiment */}
                <div
                  className={`p-4 rounded-lg ${
                    SwissEconomicDataEngine.getMarketSentiment().color ===
                    "green"
                      ? darkMode
                        ? "bg-green-800/30 border border-green-600/40"
                        : "bg-green-50 border border-green-200"
                      : SwissEconomicDataEngine.getMarketSentiment().color ===
                        "emerald"
                      ? darkMode
                        ? "bg-emerald-800/30 border border-emerald-600/40"
                        : "bg-emerald-50 border border-emerald-200"
                      : SwissEconomicDataEngine.getMarketSentiment().color ===
                        "red"
                      ? darkMode
                        ? "bg-red-800/30 border border-red-600/40"
                        : "bg-red-50 border border-red-200"
                      : SwissEconomicDataEngine.getMarketSentiment().color ===
                        "orange"
                      ? darkMode
                        ? "bg-orange-800/30 border border-orange-600/40"
                        : "bg-orange-50 border border-orange-200"
                      : darkMode
                      ? "bg-gray-700/60 border border-gray-600/40"
                      : "bg-gray-100 border border-gray-200"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-2 ${
                      SwissEconomicDataEngine.getMarketSentiment().color ===
                      "green"
                        ? darkMode
                          ? "text-green-300"
                          : "text-green-700"
                        : SwissEconomicDataEngine.getMarketSentiment().color ===
                          "emerald"
                        ? darkMode
                          ? "text-emerald-300"
                          : "text-emerald-700"
                        : SwissEconomicDataEngine.getMarketSentiment().color ===
                          "red"
                        ? darkMode
                          ? "text-red-300"
                          : "text-red-700"
                        : SwissEconomicDataEngine.getMarketSentiment().color ===
                          "orange"
                        ? darkMode
                          ? "text-orange-300"
                          : "text-orange-700"
                        : darkMode
                        ? "text-gray-300"
                        : "text-gray-700"
                    }`}
                  >
                    {SwissEconomicDataEngine.getMarketSentiment().icon} Market
                    Sentiment
                  </h4>
                  <div
                    className={`text-xl font-bold mb-1 ${
                      SwissEconomicDataEngine.getMarketSentiment().color ===
                      "green"
                        ? darkMode
                          ? "text-green-400"
                          : "text-green-600"
                        : SwissEconomicDataEngine.getMarketSentiment().color ===
                          "emerald"
                        ? darkMode
                          ? "text-emerald-400"
                          : "text-emerald-600"
                        : SwissEconomicDataEngine.getMarketSentiment().color ===
                          "red"
                        ? darkMode
                          ? "text-red-400"
                          : "text-red-600"
                        : SwissEconomicDataEngine.getMarketSentiment().color ===
                          "orange"
                        ? darkMode
                          ? "text-orange-400"
                          : "text-orange-600"
                        : darkMode
                        ? "text-gray-400"
                        : "text-gray-600"
                    }`}
                  >
                    {SwissEconomicDataEngine.getMarketSentiment()
                      .sentiment.replace("_", " ")
                      .toUpperCase()}
                  </div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    {SwissEconomicDataEngine.getMarketSentiment().description}
                  </p>
                  <div
                    className={`mt-2 text-xs ${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  >
                    Score: {SwissEconomicDataEngine.getMarketSentiment().score}
                    /100
                  </div>
                </div>
              </div>
            </div>

            {/* Economic Alerts */}
            {economicAlerts.length > 0 && (
              <div
                className={`p-5 md:p-6 rounded-xl ${
                  darkMode
                    ? "bg-gray-800/70 border border-yellow-500/25"
                    : "bg-white/90 border border-yellow-200 shadow-md"
                } backdrop-blur-sm`}
              >
                <h3
                  className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                    darkMode ? "text-white" : "text-gray-800"
                  } flex items-center`}
                >
                  <span className="mr-2 text-yellow-500 text-xl">⚠️</span>{" "}
                  Economic Alerts
                </h3>

                <div className="space-y-3">
                  {economicAlerts.map((alert, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border-l-4 ${
                        alert.severity === "high"
                          ? darkMode
                            ? "bg-red-800/30 border-red-500"
                            : "bg-red-50 border-red-500"
                          : alert.severity === "medium"
                          ? darkMode
                            ? "bg-yellow-800/30 border-yellow-500"
                            : "bg-yellow-50 border-yellow-500"
                          : darkMode
                          ? "bg-blue-800/30 border-blue-500"
                          : "bg-blue-50 border-blue-500"
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h4
                          className={`font-semibold ${
                            darkMode ? "text-white" : "text-gray-800"
                          } flex items-center`}
                        >
                          {alert.icon}{" "}
                          <span className="ml-2">{alert.title}</span>
                        </h4>
                        <span
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            alert.severity === "high"
                              ? "bg-red-500 text-white"
                              : alert.severity === "medium"
                              ? "bg-yellow-500 text-white"
                              : "bg-blue-500 text-white"
                          }`}
                        >
                          {alert.severity.toUpperCase()}
                        </span>
                      </div>
                      <p
                        className={`text-sm mb-2 ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        {alert.message}
                      </p>
                      <div
                        className={`text-xs ${
                          darkMode ? "text-gray-400" : "text-gray-500"
                        }`}
                      >
                        <strong>Impact:</strong> {alert.impact}
                      </div>
                      <div
                        className={`text-xs mt-1 ${
                          darkMode ? "text-gray-400" : "text-gray-500"
                        }`}
                      >
                        <strong>Type:</strong> {alert.type} •{" "}
                        <strong>Time:</strong>{" "}
                        {alert.timestamp.toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Dynamic Returns Configuration */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-green-500/25"
                  : "bg-white/90 border border-green-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-green-500 text-xl">🎯</span> Dynamic
                Return Modeling
              </h3>

              <div className="mb-4">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={useDynamicReturns}
                    onChange={(e) => setUseDynamicReturns(e.target.checked)}
                    className="w-5 h-5 text-green-600 rounded focus:ring-green-500"
                  />
                  <span
                    className={`text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Use real-time economic data for return assumptions
                  </span>
                </label>
                <p
                  className={`text-xs mt-1 ml-8 ${
                    darkMode ? "text-gray-400" : "text-gray-500"
                  }`}
                >
                  When enabled, your projections will automatically adjust based
                  on current Swiss economic conditions
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Current Assumptions
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Expected Return:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-green-400" : "text-green-600"
                        }`}
                      >
                        {effectiveExpectedReturn.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Inflation Rate:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        {effectiveInflationRate.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Real Return:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-purple-400" : "text-purple-600"
                        }`}
                      >
                        {(
                          effectiveExpectedReturn - effectiveInflationRate
                        ).toFixed(1)}
                        %
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Source:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          dynamicReturns.source === "dynamic"
                            ? darkMode
                              ? "text-green-400"
                              : "text-green-600"
                            : darkMode
                            ? "text-gray-400"
                            : "text-gray-600"
                        }`}
                      >
                        {dynamicReturns.source === "dynamic"
                          ? "Real-time Data"
                          : "Manual Input"}
                      </span>
                    </div>
                  </div>
                </div>

                {dynamicReturns.source === "dynamic" &&
                  dynamicReturns.breakdown && (
                    <div
                      className={`p-4 rounded-lg ${
                        darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                      }`}
                    >
                      <h4
                        className={`text-lg font-semibold mb-3 ${
                          darkMode ? "text-gray-200" : "text-gray-700"
                        }`}
                      >
                        Asset Class Returns
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Swiss Equity:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-blue-400" : "text-blue-600"
                            }`}
                          >
                            {dynamicReturns.breakdown.equity.toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Swiss Bonds:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-green-400" : "text-green-600"
                            }`}
                          >
                            {dynamicReturns.breakdown.bonds.toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Conservative:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-yellow-400" : "text-yellow-600"
                            }`}
                          >
                            {dynamicReturns.breakdown.conservative.toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span
                            className={`text-sm ${
                              darkMode ? "text-gray-300" : "text-gray-600"
                            }`}
                          >
                            Aggressive:
                          </span>
                          <span
                            className={`text-sm font-medium ${
                              darkMode ? "text-purple-400" : "text-purple-600"
                            }`}
                          >
                            {dynamicReturns.breakdown.aggressive.toFixed(1)}%
                          </span>
                        </div>
                        <div className="border-t pt-2 mt-2">
                          <div className="flex justify-between">
                            <span
                              className={`text-sm font-semibold ${
                                darkMode ? "text-white" : "text-gray-800"
                              }`}
                            >
                              Mixed Portfolio:
                            </span>
                            <span
                              className={`text-sm font-bold ${
                                darkMode ? "text-green-400" : "text-green-600"
                              }`}
                            >
                              {dynamicReturns.expectedReturn.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        {dynamicReturns.confidence && (
                          <div className="flex justify-between">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Confidence:
                            </span>
                            <span
                              className={`text-sm font-medium ${
                                dynamicReturns.confidence > 80
                                  ? darkMode
                                    ? "text-green-400"
                                    : "text-green-600"
                                  : dynamicReturns.confidence > 60
                                  ? darkMode
                                    ? "text-yellow-400"
                                    : "text-yellow-600"
                                  : darkMode
                                  ? "text-red-400"
                                  : "text-red-600"
                              }`}
                            >
                              {dynamicReturns.confidence}%
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
              </div>
            </div>

            {/* Swiss Economic Dashboard */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-blue-500/25"
                  : "bg-white/90 border border-blue-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-blue-500 text-xl">🇨🇭</span> Swiss
                Economic Dashboard
              </h3>

              <div className="grid md:grid-cols-3 gap-4">
                {/* SNB Data */}
                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Swiss National Bank
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Policy Rate:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.snbData.policyRate}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Inflation:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          SwissEconomicDataEngine.snbData.currentInflation > 2.5
                            ? darkMode
                              ? "text-red-400"
                              : "text-red-600"
                            : SwissEconomicDataEngine.snbData.currentInflation >
                              2.0
                            ? darkMode
                              ? "text-yellow-400"
                              : "text-yellow-600"
                            : darkMode
                            ? "text-green-400"
                            : "text-green-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.snbData.currentInflation}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        GDP Growth:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          SwissEconomicDataEngine.economicIndicators.gdpGrowth >
                          1.5
                            ? darkMode
                              ? "text-green-400"
                              : "text-green-600"
                            : SwissEconomicDataEngine.economicIndicators
                                .gdpGrowth > 0.5
                            ? darkMode
                              ? "text-yellow-400"
                              : "text-yellow-600"
                            : darkMode
                            ? "text-red-400"
                            : "text-red-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.economicIndicators.gdpGrowth}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Unemployment:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          SwissEconomicDataEngine.economicIndicators
                            .unemployment < 2.5
                            ? darkMode
                              ? "text-green-400"
                              : "text-green-600"
                            : SwissEconomicDataEngine.economicIndicators
                                .unemployment < 3.5
                            ? darkMode
                              ? "text-yellow-400"
                              : "text-yellow-600"
                            : darkMode
                            ? "text-red-400"
                            : "text-red-600"
                        }`}
                      >
                        {
                          SwissEconomicDataEngine.economicIndicators
                            .unemployment
                        }
                        %
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Next Meeting:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-purple-400" : "text-purple-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.snbData.nextMeeting.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Market Data */}
                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Swiss Markets
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        SMI Index:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          SwissEconomicDataEngine.marketData.smi
                            .changePercent >= 0
                            ? darkMode
                              ? "text-green-400"
                              : "text-green-600"
                            : darkMode
                            ? "text-red-400"
                            : "text-red-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.marketData.smi.current.toLocaleString()}{" "}
                        (
                        {SwissEconomicDataEngine.marketData.smi.changePercent >=
                        0
                          ? "+"
                          : ""}
                        {(
                          SwissEconomicDataEngine.marketData.smi.changePercent *
                          100
                        ).toFixed(1)}
                        %)
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        YTD Return:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          SwissEconomicDataEngine.marketData.smi.ytdReturn >= 0
                            ? darkMode
                              ? "text-green-400"
                              : "text-green-600"
                            : darkMode
                            ? "text-red-400"
                            : "text-red-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.marketData.smi.ytdReturn >= 0
                          ? "+"
                          : ""}
                        {SwissEconomicDataEngine.marketData.smi.ytdReturn}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        10Y Bond:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.marketData.bonds.swiss10Year}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Volatility:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          SwissEconomicDataEngine.marketData.smi.volatility > 25
                            ? darkMode
                              ? "text-red-400"
                              : "text-red-600"
                            : SwissEconomicDataEngine.marketData.smi
                                .volatility > 15
                            ? darkMode
                              ? "text-yellow-400"
                              : "text-yellow-600"
                            : darkMode
                            ? "text-green-400"
                            : "text-green-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.marketData.smi.volatility}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Corporate Bonds:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-purple-400" : "text-purple-600"
                        }`}
                      >
                        {
                          SwissEconomicDataEngine.marketData.bonds
                            .corporateBonds
                        }
                        %
                      </span>
                    </div>
                  </div>
                </div>

                {/* Currency & Economic Indicators */}
                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Currency & Indicators
                  </h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        CHF/EUR:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.currencyData.chfEur.toFixed(4)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        CHF/USD:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-green-400" : "text-green-600"
                        }`}
                      >
                        {SwissEconomicDataEngine.currencyData.chfUsd.toFixed(4)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Consumer Confidence:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          SwissEconomicDataEngine.economicIndicators
                            .consumerConfidence > 100
                            ? darkMode
                              ? "text-green-400"
                              : "text-green-600"
                            : SwissEconomicDataEngine.economicIndicators
                                .consumerConfidence > 90
                            ? darkMode
                              ? "text-yellow-400"
                              : "text-yellow-600"
                            : darkMode
                            ? "text-red-400"
                            : "text-red-600"
                        }`}
                      >
                        {
                          SwissEconomicDataEngine.economicIndicators
                            .consumerConfidence
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Manufacturing PMI:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          SwissEconomicDataEngine.economicIndicators
                            .manufacturingPMI > 50
                            ? darkMode
                              ? "text-green-400"
                              : "text-green-600"
                            : darkMode
                            ? "text-red-400"
                            : "text-red-600"
                        }`}
                      >
                        {
                          SwissEconomicDataEngine.economicIndicators
                            .manufacturingPMI
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Housing Index:
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-purple-400" : "text-purple-600"
                        }`}
                      >
                        {
                          SwissEconomicDataEngine.economicIndicators
                            .housingPriceIndex
                        }
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div
                className={`mt-4 p-3 rounded-lg ${
                  darkMode
                    ? "bg-blue-800/30 border border-blue-600/40"
                    : "bg-blue-50 border border-blue-200"
                }`}
              >
                <div className="flex justify-between items-center text-sm">
                  <span
                    className={`${
                      darkMode ? "text-blue-300" : "text-blue-700"
                    }`}
                  >
                    Last Updated:{" "}
                    {SwissEconomicDataEngine.snbData.lastUpdate.toLocaleString()}
                  </span>
                  <button
                    onClick={fetchEconomicData}
                    className={`px-3 py-1 rounded text-xs font-medium ${
                      darkMode
                        ? "bg-blue-600 text-white hover:bg-blue-700"
                        : "bg-blue-500 text-white hover:bg-blue-600"
                    } transition-colors`}
                  >
                    Refresh Data
                  </button>
                </div>
              </div>
            </div>

            {/* SNB Rate History */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-purple-500/25"
                  : "bg-white/90 border border-purple-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-purple-500 text-xl">📊</span> SNB
                Policy Rate History
              </h3>

              <div className="overflow-x-auto">
                <table
                  className={`w-full text-sm ${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  }`}
                >
                  <thead>
                    <tr
                      className={`border-b ${
                        darkMode ? "border-gray-600" : "border-gray-200"
                      }`}
                    >
                      <th
                        className={`text-left py-2 px-3 font-semibold ${
                          darkMode ? "text-gray-200" : "text-gray-700"
                        }`}
                      >
                        Date
                      </th>
                      <th
                        className={`text-left py-2 px-3 font-semibold ${
                          darkMode ? "text-gray-200" : "text-gray-700"
                        }`}
                      >
                        Policy Rate
                      </th>
                      <th
                        className={`text-left py-2 px-3 font-semibold ${
                          darkMode ? "text-gray-200" : "text-gray-700"
                        }`}
                      >
                        Inflation
                      </th>
                      <th
                        className={`text-left py-2 px-3 font-semibold ${
                          darkMode ? "text-gray-200" : "text-gray-700"
                        }`}
                      >
                        Change
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {SwissEconomicDataEngine.snbData.rateHistory.map(
                      (entry, index) => {
                        const previousEntry =
                          SwissEconomicDataEngine.snbData.rateHistory[
                            index + 1
                          ];
                        const rateChange = previousEntry
                          ? entry.rate - previousEntry.rate
                          : 0;

                        return (
                          <tr
                            key={entry.date}
                            className={`border-b ${
                              darkMode ? "border-gray-700" : "border-gray-100"
                            } hover:${
                              darkMode ? "bg-gray-700/30" : "bg-gray-50"
                            }`}
                          >
                            <td className="py-2 px-3">
                              {new Date(entry.date).toLocaleDateString()}
                            </td>
                            <td className="py-2 px-3 font-medium">
                              {entry.rate}%
                            </td>
                            <td className="py-2 px-3">{entry.inflation}%</td>
                            <td className="py-2 px-3">
                              {rateChange === 0 ? (
                                <span
                                  className={`text-xs px-2 py-1 rounded ${
                                    darkMode
                                      ? "bg-gray-600 text-gray-300"
                                      : "bg-gray-200 text-gray-600"
                                  }`}
                                >
                                  No change
                                </span>
                              ) : rateChange > 0 ? (
                                <span className="text-xs px-2 py-1 rounded bg-red-500 text-white">
                                  +{rateChange.toFixed(2)}%
                                </span>
                              ) : (
                                <span className="text-xs px-2 py-1 rounded bg-green-500 text-white">
                                  {rateChange.toFixed(2)}%
                                </span>
                              )}
                            </td>
                          </tr>
                        );
                      }
                    )}
                  </tbody>
                </table>
              </div>

              <div
                className={`mt-4 p-3 rounded-lg ${
                  darkMode
                    ? "bg-purple-800/30 border border-purple-600/40"
                    : "bg-purple-50 border border-purple-200"
                }`}
              >
                <p
                  className={`text-sm ${
                    darkMode ? "text-purple-300" : "text-purple-700"
                  }`}
                >
                  <strong>Next SNB Meeting:</strong>{" "}
                  {SwissEconomicDataEngine.snbData.nextMeeting.toLocaleDateString()}{" "}
                  - The Swiss National Bank typically meets quarterly to assess
                  monetary policy and economic conditions.
                </p>
              </div>
            </div>
          </div>
        )}

        {activeTab === "advancedAnalytics" && (
          <div className="space-y-5 md:space-y-6">
            {/* Monte Carlo Simulation Controls */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-purple-500/25"
                  : "bg-white/90 border border-purple-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-purple-500 text-xl">🧠</span>{" "}
                Advanced Monte Carlo Analysis
              </h3>

              <div className="grid md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Stress Test Scenario
                  </label>
                  <select
                    value={stressTestScenario}
                    onChange={(e) => setStressTestScenario(e.target.value)}
                    className={`w-full p-3 rounded-lg border ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-white border-gray-300 text-gray-800"
                    } focus:ring-2 focus:ring-purple-500`}
                  >
                    <option value="baseCase">Base Case (Normal Markets)</option>
                    <option value="bearMarket">Bear Market</option>
                    <option value="highInflation">High Inflation</option>
                    <option value="recession">Recession</option>
                    <option value="stagflation">Stagflation</option>
                  </select>
                </div>

                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Simulation Iterations
                  </label>
                  <select
                    value={simulationIterations}
                    onChange={(e) =>
                      setSimulationIterations(parseInt(e.target.value))
                    }
                    className={`w-full p-3 rounded-lg border ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-white border-gray-300 text-gray-800"
                    } focus:ring-2 focus:ring-purple-500`}
                  >
                    <option value={500}>500 (Fast)</option>
                    <option value={1000}>1,000 (Standard)</option>
                    <option value={5000}>5,000 (Detailed)</option>
                    <option value={10000}>10,000 (Comprehensive)</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <button
                    onClick={runMonteCarloSimulation}
                    disabled={simulationRunning}
                    className={`w-full px-4 py-3 rounded-lg font-medium ${
                      simulationRunning
                        ? darkMode
                          ? "bg-gray-600 text-gray-400"
                          : "bg-gray-300 text-gray-500"
                        : darkMode
                        ? "bg-purple-600 text-white hover:bg-purple-700"
                        : "bg-purple-500 text-white hover:bg-purple-600"
                    } transition-colors`}
                  >
                    {simulationRunning ? "Running..." : "Run Simulation"}
                  </button>
                </div>
              </div>

              {simulationRunning && (
                <div
                  className={`p-4 rounded-lg ${
                    darkMode
                      ? "bg-blue-800/30 border border-blue-600/40"
                      : "bg-blue-50 border border-blue-200"
                  }`}
                >
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-3"></div>
                    <span
                      className={`text-sm ${
                        darkMode ? "text-blue-300" : "text-blue-700"
                      }`}
                    >
                      Running {simulationIterations.toLocaleString()}{" "}
                      simulations for{" "}
                      {
                        MonteCarloEngine.generateStressTestScenarios()[
                          stressTestScenario
                        ]?.name
                      }
                      ...
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Monte Carlo Results */}
            {monteCarloResults && (
              <>
                {/* Success Rate and Risk Assessment */}
                <div
                  className={`p-5 md:p-6 rounded-xl ${
                    darkMode
                      ? "bg-gray-800/70 border border-green-500/25"
                      : "bg-white/90 border border-green-200 shadow-md"
                  } backdrop-blur-sm`}
                >
                  <h3
                    className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                      darkMode ? "text-white" : "text-gray-800"
                    } flex items-center`}
                  >
                    <span className="mr-2 text-green-500 text-xl">📊</span>{" "}
                    Simulation Results
                  </h3>

                  <div className="grid md:grid-cols-4 gap-4">
                    <div
                      className={`p-4 rounded-lg text-center ${
                        darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                      }`}
                    >
                      <div
                        className={`text-2xl font-bold ${
                          monteCarloResults.successRate > 90
                            ? "text-green-500"
                            : monteCarloResults.successRate > 75
                            ? "text-yellow-500"
                            : "text-red-500"
                        }`}
                      >
                        {monteCarloResults.successRate.toFixed(1)}%
                      </div>
                      <div
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Success Rate
                      </div>
                    </div>

                    <div
                      className={`p-4 rounded-lg text-center ${
                        darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                      }`}
                    >
                      <div
                        className={`text-2xl font-bold ${
                          darkMode ? "text-blue-400" : "text-blue-600"
                        }`}
                      >
                        {formatCurrency(monteCarloResults.percentiles.p50)}
                      </div>
                      <div
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Median Outcome
                      </div>
                    </div>

                    <div
                      className={`p-4 rounded-lg text-center ${
                        darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                      }`}
                    >
                      <div
                        className={`text-2xl font-bold ${
                          darkMode ? "text-red-400" : "text-red-600"
                        }`}
                      >
                        {formatCurrency(monteCarloResults.percentiles.p10)}
                      </div>
                      <div
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Worst 10%
                      </div>
                    </div>

                    <div
                      className={`p-4 rounded-lg text-center ${
                        darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                      }`}
                    >
                      <div
                        className={`text-2xl font-bold ${
                          darkMode ? "text-green-400" : "text-green-600"
                        }`}
                      >
                        {formatCurrency(monteCarloResults.percentiles.p90)}
                      </div>
                      <div
                        className={`text-sm ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        Best 10%
                      </div>
                    </div>
                  </div>

                  <div
                    className={`mt-4 p-4 rounded-lg ${
                      darkMode
                        ? "bg-blue-800/30 border border-blue-600/40"
                        : "bg-blue-50 border border-blue-200"
                    }`}
                  >
                    <h4
                      className={`font-semibold mb-2 ${
                        darkMode ? "text-blue-300" : "text-blue-700"
                      }`}
                    >
                      📈 Scenario: {monteCarloResults.scenario.name}
                    </h4>
                    <div className="grid md:grid-cols-3 gap-3 text-sm">
                      <div>
                        <span
                          className={`${
                            darkMode ? "text-blue-200" : "text-blue-600"
                          }`}
                        >
                          Expected Return:
                        </span>
                        <span
                          className={`ml-2 font-medium ${
                            darkMode ? "text-white" : "text-gray-800"
                          }`}
                        >
                          {monteCarloResults.scenario.expectedReturn}%
                        </span>
                      </div>
                      <div>
                        <span
                          className={`${
                            darkMode ? "text-blue-200" : "text-blue-600"
                          }`}
                        >
                          Volatility:
                        </span>
                        <span
                          className={`ml-2 font-medium ${
                            darkMode ? "text-white" : "text-gray-800"
                          }`}
                        >
                          {monteCarloResults.scenario.volatility}%
                        </span>
                      </div>
                      <div>
                        <span
                          className={`${
                            darkMode ? "text-blue-200" : "text-blue-600"
                          }`}
                        >
                          Iterations:
                        </span>
                        <span
                          className={`ml-2 font-medium ${
                            darkMode ? "text-white" : "text-gray-800"
                          }`}
                        >
                          {monteCarloResults.iterations.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Safe Withdrawal Rate Analysis */}
                {advancedAnalytics && (
                  <div
                    className={`p-5 md:p-6 rounded-xl ${
                      darkMode
                        ? "bg-gray-800/70 border border-orange-500/25"
                        : "bg-white/90 border border-orange-200 shadow-md"
                    } backdrop-blur-sm`}
                  >
                    <h3
                      className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                        darkMode ? "text-white" : "text-gray-800"
                      } flex items-center`}
                    >
                      <span className="mr-2 text-orange-500 text-xl">💰</span>{" "}
                      Safe Withdrawal Strategy
                    </h3>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div
                        className={`p-4 rounded-lg ${
                          darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                        }`}
                      >
                        <h4
                          className={`text-lg font-semibold mb-3 ${
                            darkMode ? "text-gray-200" : "text-gray-700"
                          }`}
                        >
                          Recommended Withdrawal Rate
                        </h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Safe Rate:
                            </span>
                            <span
                              className={`text-lg font-bold ${
                                darkMode ? "text-orange-400" : "text-orange-600"
                              }`}
                            >
                              {advancedAnalytics.withdrawalStrategy.safeRate.toFixed(
                                1
                              )}
                              %
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Confidence:
                            </span>
                            <span
                              className={`text-sm font-medium ${
                                darkMode ? "text-green-400" : "text-green-600"
                              }`}
                            >
                              {advancedAnalytics.withdrawalStrategy.confidence.toFixed(
                                1
                              )}
                              %
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Annual Income:
                            </span>
                            <span
                              className={`text-sm font-medium ${
                                darkMode ? "text-blue-400" : "text-blue-600"
                              }`}
                            >
                              {formatCurrency(
                                advancedAnalytics.withdrawalStrategy
                                  .annualIncome
                              )}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Monthly Income:
                            </span>
                            <span
                              className={`text-sm font-medium ${
                                darkMode ? "text-blue-400" : "text-blue-600"
                              }`}
                            >
                              {formatCurrency(
                                advancedAnalytics.withdrawalStrategy
                                  .monthlyIncome
                              )}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div
                        className={`p-4 rounded-lg ${
                          darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                        }`}
                      >
                        <h4
                          className={`text-lg font-semibold mb-3 ${
                            darkMode ? "text-gray-200" : "text-gray-700"
                          }`}
                        >
                          Risk Assessment
                        </h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Risk Level:
                            </span>
                            <span
                              className={`text-sm font-medium ${
                                advancedAnalytics.riskAssessment.level === "Low"
                                  ? "text-green-500"
                                  : advancedAnalytics.riskAssessment.level ===
                                    "Medium"
                                  ? "text-yellow-500"
                                  : "text-red-500"
                              }`}
                            >
                              {advancedAnalytics.riskAssessment.level}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Success Probability:
                            </span>
                            <span
                              className={`text-sm font-medium ${
                                darkMode ? "text-green-400" : "text-green-600"
                              }`}
                            >
                              {advancedAnalytics.riskAssessment.successProbability.toFixed(
                                1
                              )}
                              %
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Worst Case:
                            </span>
                            <span
                              className={`text-sm font-medium ${
                                darkMode ? "text-red-400" : "text-red-600"
                              }`}
                            >
                              {formatCurrency(
                                advancedAnalytics.riskAssessment
                                  .worstCaseScenario
                              )}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span
                              className={`text-sm ${
                                darkMode ? "text-gray-300" : "text-gray-600"
                              }`}
                            >
                              Best Case:
                            </span>
                            <span
                              className={`text-sm font-medium ${
                                darkMode ? "text-green-400" : "text-green-600"
                              }`}
                            >
                              {formatCurrency(
                                advancedAnalytics.riskAssessment
                                  .bestCaseScenario
                              )}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Optimization Suggestions */}
                {advancedAnalytics &&
                  advancedAnalytics.optimizationSuggestions.length > 0 && (
                    <div
                      className={`p-5 md:p-6 rounded-xl ${
                        darkMode
                          ? "bg-gray-800/70 border border-yellow-500/25"
                          : "bg-white/90 border border-yellow-200 shadow-md"
                      } backdrop-blur-sm`}
                    >
                      <h3
                        className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                          darkMode ? "text-white" : "text-gray-800"
                        } flex items-center`}
                      >
                        <span className="mr-2 text-yellow-500 text-xl">💡</span>{" "}
                        Optimization Recommendations
                      </h3>

                      <div className="space-y-3">
                        {advancedAnalytics.optimizationSuggestions.map(
                          (suggestion, index) => (
                            <div
                              key={index}
                              className={`p-4 rounded-lg border-l-4 ${
                                suggestion.priority === "high"
                                  ? darkMode
                                    ? "bg-red-800/30 border-red-500"
                                    : "bg-red-50 border-red-500"
                                  : darkMode
                                  ? "bg-yellow-800/30 border-yellow-500"
                                  : "bg-yellow-50 border-yellow-500"
                              }`}
                            >
                              <div className="flex justify-between items-start mb-2">
                                <h4
                                  className={`text-lg font-semibold ${
                                    darkMode ? "text-white" : "text-gray-800"
                                  }`}
                                >
                                  {suggestion.title}
                                </h4>
                                <span
                                  className={`px-2 py-1 rounded text-xs font-medium ${
                                    suggestion.priority === "high"
                                      ? "bg-red-500 text-white"
                                      : "bg-yellow-500 text-white"
                                  }`}
                                >
                                  {suggestion.priority.toUpperCase()}
                                </span>
                              </div>
                              <p
                                className={`text-sm mb-2 ${
                                  darkMode ? "text-gray-300" : "text-gray-600"
                                }`}
                              >
                                {suggestion.description}
                              </p>
                              <div
                                className={`text-xs ${
                                  darkMode ? "text-gray-400" : "text-gray-500"
                                }`}
                              >
                                <strong>Expected Impact:</strong>{" "}
                                {suggestion.impact}
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
              </>
            )}

            {/* Getting Started with Advanced Analytics */}
            {!monteCarloResults && (
              <div
                className={`p-5 md:p-6 rounded-xl ${
                  darkMode
                    ? "bg-gray-800/70 border border-blue-500/25"
                    : "bg-white/90 border border-blue-200 shadow-md"
                } backdrop-blur-sm`}
              >
                <h3
                  className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                    darkMode ? "text-white" : "text-gray-800"
                  } flex items-center`}
                >
                  <span className="mr-2 text-blue-500 text-xl">🚀</span>{" "}
                  Advanced Analytics Overview
                </h3>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4
                      className={`text-lg font-semibold mb-3 ${
                        darkMode ? "text-gray-200" : "text-gray-700"
                      }`}
                    >
                      Monte Carlo Simulation
                    </h4>
                    <ul
                      className={`space-y-2 text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      <li>
                        • Run thousands of scenarios to test your FIRE plan
                      </li>
                      <li>
                        • Account for market volatility and economic uncertainty
                      </li>
                      <li>
                        • Calculate probability of success under different
                        conditions
                      </li>
                      <li>
                        • Stress test against bear markets, recessions, and
                        inflation
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h4
                      className={`text-lg font-semibold mb-3 ${
                        darkMode ? "text-gray-200" : "text-gray-700"
                      }`}
                    >
                      Safe Withdrawal Analysis
                    </h4>
                    <ul
                      className={`space-y-2 text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      <li>
                        • Determine optimal withdrawal rate for retirement
                      </li>
                      <li>• Account for sequence of returns risk</li>
                      <li>• Calculate sustainable income levels</li>
                      <li>• Optimize for 30+ year retirement periods</li>
                    </ul>
                  </div>
                </div>

                <div
                  className={`mt-6 p-4 rounded-lg ${
                    darkMode
                      ? "bg-blue-800/30 border border-blue-600/40"
                      : "bg-blue-50 border border-blue-200"
                  }`}
                >
                  <p
                    className={`text-sm ${
                      darkMode ? "text-blue-200" : "text-blue-700"
                    }`}
                  >
                    💡 <strong>Get Started:</strong> Select a stress test
                    scenario above and click "Run Simulation" to generate
                    comprehensive analytics for your FIRE plan. This analysis
                    will help you understand the probability of success and
                    optimize your strategy for different economic conditions.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === "fireAcceleration" && (
          <div className="space-y-5 md:space-y-6">
            {/* Current Timeline Overview */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-blue-500/25"
                  : "bg-white/90 border border-blue-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-blue-500 text-xl">🚀</span> FIRE
                Acceleration Engine
              </h3>

              <div className="grid md:grid-cols-3 gap-4 mb-4">
                <div
                  className={`p-4 rounded-lg text-center ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <div
                    className={`text-2xl font-bold ${
                      darkMode ? "text-blue-400" : "text-blue-600"
                    }`}
                  >
                    {yearsToFire.toFixed(1)} years
                  </div>
                  <div
                    className={`text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    Current Timeline
                  </div>
                </div>

                <div
                  className={`p-4 rounded-lg text-center ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <div
                    data-testid="savings-rate"
                    className={`text-2xl font-bold ${
                      darkMode ? "text-green-400" : "text-green-600"
                    }`}
                  >
                    {(
                      (totalSavingsDisplay / (parseFloat(monthlyIncome) || 1)) *
                      100
                    ).toFixed(1)}
                    %
                  </div>
                  <div
                    className={`text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    Savings Rate
                  </div>
                </div>

                <div
                  className={`p-4 rounded-lg text-center ${
                    darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                  }`}
                >
                  <div
                    className={`text-2xl font-bold ${
                      darkMode ? "text-purple-400" : "text-purple-600"
                    }`}
                  >
                    {formatCurrency(totalSavingsDisplay)}
                  </div>
                  <div
                    className={`text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    Monthly Savings
                  </div>
                </div>
              </div>

              <div
                className={`p-4 rounded-lg ${
                  darkMode
                    ? "bg-blue-800/30 border border-blue-600/40"
                    : "bg-blue-50 border border-blue-200"
                }`}
              >
                <p
                  className={`text-sm ${
                    darkMode ? "text-blue-200" : "text-blue-700"
                  }`}
                >
                  💡 <strong>Acceleration Opportunity:</strong> Small changes to
                  your financial strategy can dramatically reduce your time to
                  FIRE. Explore the recommendations below to discover how to
                  retire years earlier with targeted optimizations.
                </p>
              </div>
            </div>

            {/* Quick Wins Section */}
            {quickWins.length > 0 && (
              <div
                className={`p-5 md:p-6 rounded-xl ${
                  darkMode
                    ? "bg-gray-800/70 border border-green-500/25"
                    : "bg-white/90 border border-green-200 shadow-md"
                } backdrop-blur-sm`}
              >
                <h3
                  className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                    darkMode ? "text-white" : "text-gray-800"
                  } flex items-center`}
                >
                  <span className="mr-2 text-green-500 text-xl">⚡</span> Quick
                  Wins (Easy Implementation)
                </h3>

                <div className="space-y-3">
                  {quickWins.map((recommendation, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border-l-4 border-green-500 ${
                        darkMode ? "bg-green-800/20" : "bg-green-50"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4
                          className={`text-lg font-semibold ${
                            darkMode ? "text-white" : "text-gray-800"
                          }`}
                        >
                          {recommendation.action}
                        </h4>
                        <span className="px-2 py-1 rounded text-xs font-medium bg-green-500 text-white">
                          EASY
                        </span>
                      </div>

                      <div className="grid md:grid-cols-3 gap-3 text-sm">
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-green-200" : "text-green-700"
                            }`}
                          >
                            Timeline Savings:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            {recommendation.timelineSavings.toFixed(1)} years
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-green-200" : "text-green-700"
                            }`}
                          >
                            Monthly Impact:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            +{formatCurrency(recommendation.monthlyIncrease)}
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-green-200" : "text-green-700"
                            }`}
                          >
                            Lifetime Value:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            {formatCurrency(recommendation.lifetimeValue)}
                          </span>
                        </div>
                      </div>

                      <div
                        className={`mt-3 text-xs ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        <strong>New Timeline:</strong>{" "}
                        {recommendation.newTimeline.toFixed(1)} years (retire at
                        age{" "}
                        {(
                          (parseInt(currentAge) || 30) +
                          recommendation.newTimeline
                        ).toFixed(0)}
                        )
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* High Impact Section */}
            {highImpact.length > 0 && (
              <div
                className={`p-5 md:p-6 rounded-xl ${
                  darkMode
                    ? "bg-gray-800/70 border border-orange-500/25"
                    : "bg-white/90 border border-orange-200 shadow-md"
                } backdrop-blur-sm`}
              >
                <h3
                  className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                    darkMode ? "text-white" : "text-gray-800"
                  } flex items-center`}
                >
                  <span className="mr-2 text-orange-500 text-xl">🎯</span> High
                  Impact Opportunities
                </h3>

                <div className="space-y-3">
                  {highImpact.map((recommendation, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border-l-4 border-orange-500 ${
                        darkMode ? "bg-orange-800/20" : "bg-orange-50"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4
                          className={`text-lg font-semibold ${
                            darkMode ? "text-white" : "text-gray-800"
                          }`}
                        >
                          {recommendation.action}
                        </h4>
                        <span
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            recommendation.difficulty === "easy"
                              ? "bg-green-500"
                              : recommendation.difficulty === "moderate"
                              ? "bg-yellow-500"
                              : "bg-red-500"
                          } text-white`}
                        >
                          {recommendation.difficulty.toUpperCase()}
                        </span>
                      </div>

                      <div className="grid md:grid-cols-3 gap-3 text-sm">
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-orange-200" : "text-orange-700"
                            }`}
                          >
                            Timeline Savings:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            {recommendation.timelineSavings.toFixed(1)} years
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-orange-200" : "text-orange-700"
                            }`}
                          >
                            Monthly Impact:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            +{formatCurrency(recommendation.monthlyIncrease)}
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-orange-200" : "text-orange-700"
                            }`}
                          >
                            Lifetime Value:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            {formatCurrency(recommendation.lifetimeValue)}
                          </span>
                        </div>
                      </div>

                      <div
                        className={`mt-3 text-xs ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        <strong>New Timeline:</strong>{" "}
                        {recommendation.newTimeline.toFixed(1)} years (retire at
                        age{" "}
                        {(
                          (parseInt(currentAge) || 30) +
                          recommendation.newTimeline
                        ).toFixed(0)}
                        )
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* All Recommendations */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-purple-500/25"
                  : "bg-white/90 border border-purple-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-purple-500 text-xl">📋</span> All
                Acceleration Recommendations
              </h3>

              {accelerationRecommendations.length > 0 ? (
                <div className="space-y-3">
                  {accelerationRecommendations.map((recommendation, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border ${
                        darkMode
                          ? "bg-gray-700/40 border-gray-600"
                          : "bg-gray-50 border-gray-200"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4
                          className={`text-lg font-semibold ${
                            darkMode ? "text-white" : "text-gray-800"
                          }`}
                        >
                          {recommendation.action}
                        </h4>
                        <div className="flex gap-2">
                          <span
                            className={`px-2 py-1 rounded text-xs font-medium ${
                              recommendation.priority === "high"
                                ? "bg-red-500"
                                : "bg-yellow-500"
                            } text-white`}
                          >
                            {recommendation.priority.toUpperCase()}
                          </span>
                          <span
                            className={`px-2 py-1 rounded text-xs font-medium ${
                              recommendation.difficulty === "easy"
                                ? "bg-green-500"
                                : recommendation.difficulty === "moderate"
                                ? "bg-yellow-500"
                                : "bg-red-500"
                            } text-white`}
                          >
                            {recommendation.difficulty.toUpperCase()}
                          </span>
                        </div>
                      </div>

                      <div className="grid md:grid-cols-4 gap-3 text-sm">
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-purple-200" : "text-purple-700"
                            }`}
                          >
                            Timeline Savings:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            {recommendation.timelineSavings.toFixed(1)} years
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-purple-200" : "text-purple-700"
                            }`}
                          >
                            Monthly Impact:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            +{formatCurrency(recommendation.monthlyIncrease)}
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-purple-200" : "text-purple-700"
                            }`}
                          >
                            Annual Impact:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            +{formatCurrency(recommendation.annualIncrease)}
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-purple-200" : "text-purple-700"
                            }`}
                          >
                            Category:
                          </span>
                          <span
                            className={`ml-2 font-medium ${
                              darkMode ? "text-white" : "text-gray-800"
                            } capitalize`}
                          >
                            {recommendation.category}
                          </span>
                        </div>
                      </div>

                      <div
                        className={`mt-3 text-xs ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        <strong>New Timeline:</strong>{" "}
                        {recommendation.newTimeline.toFixed(1)} years (retire at
                        age{" "}
                        {(
                          (parseInt(currentAge) || 30) +
                          recommendation.newTimeline
                        ).toFixed(0)}
                        ) •<strong> Lifetime Value:</strong>{" "}
                        {formatCurrency(recommendation.lifetimeValue)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/40" : "bg-gray-100"
                  } text-center`}
                >
                  <p
                    className={`${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    🎉 Your current plan is already highly optimized! Consider
                    exploring the Advanced Analytics tab for stress testing and
                    risk analysis.
                  </p>
                </div>
              )}
            </div>

            {/* Implementation Guide */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-yellow-500/25"
                  : "bg-white/90 border border-yellow-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-yellow-500 text-xl">🛠️</span>{" "}
                Implementation Strategy
              </h3>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Start with Quick Wins
                  </h4>
                  <ul
                    className={`space-y-2 text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    <li>• Focus on easy implementations first</li>
                    <li>• Build momentum with early successes</li>
                    <li>• Track progress monthly</li>
                    <li>• Celebrate milestones achieved</li>
                  </ul>
                </div>

                <div>
                  <h4
                    className={`text-lg font-semibold mb-3 ${
                      darkMode ? "text-gray-200" : "text-gray-700"
                    }`}
                  >
                    Plan High-Impact Changes
                  </h4>
                  <ul
                    className={`space-y-2 text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    <li>• Research and prepare for complex changes</li>
                    <li>• Set realistic timelines for implementation</li>
                    <li>• Consider professional advice when needed</li>
                    <li>• Monitor results and adjust as necessary</li>
                  </ul>
                </div>
              </div>

              <div
                className={`mt-6 p-4 rounded-lg ${
                  darkMode
                    ? "bg-yellow-800/30 border border-yellow-600/40"
                    : "bg-yellow-50 border border-yellow-200"
                }`}
              >
                <p
                  className={`text-sm ${
                    darkMode ? "text-yellow-200" : "text-yellow-700"
                  }`}
                >
                  💡 <strong>Pro Tip:</strong> Implement 1-2 recommendations per
                  month to avoid overwhelming yourself. Small, consistent
                  changes compound over time to create dramatic improvements in
                  your FIRE timeline.
                </p>
              </div>
            </div>
          </div>
        )}

        {activeTab === "data" && (
          <div className="space-y-5 md:space-y-6">
            {/* Data Management Panel */}
            <DataManagementPanel
              darkMode={darkMode}
              currentPlanName={currentPlanName}
              setCurrentPlanName={setCurrentPlanName}
              onLoadPlan={handleLoadPlan}
              onDeletePlan={handleDeletePlan}
              onExport={handleExport}
              onImport={handleImport}
              lastSaved={lastSaved}
            />

            {/* Historical Chart */}
            {snapshots && snapshots.length >= 2 && (
              <div
                className={`p-5 md:p-6 rounded-xl ${
                  darkMode
                    ? "bg-gray-800/70 border-blue-500/25"
                    : "bg-white/90 border-blue-200 shadow-md"
                } backdrop-blur-sm`}
              >
                <h3
                  className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                    darkMode ? "text-white" : "text-gray-800"
                  } flex items-center`}
                >
                  <span className="mr-2 text-blue-400 text-xl">📈</span>{" "}
                  Historical Trends
                </h3>
                <p
                  className={`mb-4 text-xs sm:text-sm ${
                    darkMode ? "text-gray-400" : "text-gray-600"
                  }`}
                >
                  Track your financial progress over time. Data points are
                  automatically saved monthly.
                </p>
                <HistoricalChart
                  darkMode={darkMode}
                  snapshots={snapshots}
                  formatCurrency={formatCurrency}
                />

                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-3">
                  {snapshots.length > 0 &&
                    (() => {
                      const latest = snapshots[snapshots.length - 1];
                      const oldest = snapshots[0];
                      const savingsRateChange =
                        latest.savingsRate - oldest.savingsRate;
                      const netWorthChange = latest.netWorth - oldest.netWorth;

                      return [
                        {
                          label: "Data Points",
                          value: snapshots.length,
                          icon: "📊",
                          color: darkMode ? "text-blue-400" : "text-blue-600",
                        },
                        {
                          label: "Savings Rate Δ",
                          value: `${
                            savingsRateChange >= 0 ? "+" : ""
                          }${savingsRateChange.toFixed(1)}%`,
                          icon: savingsRateChange >= 0 ? "📈" : "📉",
                          color:
                            savingsRateChange >= 0
                              ? darkMode
                                ? "text-green-400"
                                : "text-green-600"
                              : darkMode
                              ? "text-red-400"
                              : "text-red-600",
                        },
                        {
                          label: "Net Worth Δ",
                          value: formatCurrency(netWorthChange),
                          icon: netWorthChange >= 0 ? "💰" : "📉",
                          color:
                            netWorthChange >= 0
                              ? darkMode
                                ? "text-green-400"
                                : "text-green-600"
                              : darkMode
                              ? "text-red-400"
                              : "text-red-600",
                        },
                        {
                          label: "Time Period",
                          value: `${Math.round(
                            (new Date(latest.timestamp) -
                              new Date(oldest.timestamp)) /
                              (1000 * 60 * 60 * 24 * 30)
                          )} mo`,
                          icon: "📅",
                          color: darkMode
                            ? "text-purple-400"
                            : "text-purple-600",
                        },
                      ];
                    })().map((stat) => (
                      <div
                        key={stat.label}
                        className={`p-3 rounded-lg ${
                          darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                        }`}
                      >
                        <div
                          className={`text-xs font-medium mb-1 ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          <span className="mr-1">{stat.icon}</span>
                          {stat.label}
                        </div>
                        <div className={`text-sm font-bold ${stat.color}`}>
                          {stat.value}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* No Historical Data Message */}
            {(!snapshots || snapshots.length < 2) && (
              <div
                className={`p-5 md:p-6 rounded-xl ${
                  darkMode
                    ? "bg-gray-800/70 border-yellow-500/25"
                    : "bg-white/90 border-yellow-200 shadow-md"
                } backdrop-blur-sm text-center`}
              >
                <div className="text-4xl mb-3">📊</div>
                <h3
                  className={`text-lg font-semibold mb-2 ${
                    darkMode ? "text-white" : "text-gray-800"
                  }`}
                >
                  Historical Data Coming Soon
                </h3>
                <p
                  className={`text-sm ${
                    darkMode ? "text-gray-400" : "text-gray-600"
                  }`}
                >
                  Keep using Swiss Budget Pro to build your financial history.
                  Charts and trends will appear once you have multiple data
                  points saved over time.
                </p>
                <div
                  className={`mt-4 p-3 rounded-lg ${
                    darkMode ? "bg-blue-800/30" : "bg-blue-50"
                  }`}
                >
                  <p
                    className={`text-xs ${
                      darkMode ? "text-blue-300" : "text-blue-700"
                    }`}
                  >
                    💡 <strong>Tip:</strong> Data is automatically saved every
                    30 seconds. Create different scenarios using "Save As" to
                    compare planning strategies.
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === "swissRelocation" && (
          <div className="space-y-5 md:space-y-6">
            {/* Relocation Analysis Controls */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-blue-500/25"
                  : "bg-white/90 border border-blue-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <h3
                className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                  darkMode ? "text-white" : "text-gray-800"
                } flex items-center`}
              >
                <span className="mr-2 text-blue-500 text-xl">🗺️</span> Swiss
                Relocation ROI Calculator
              </h3>

              <div className="grid md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Current Canton
                  </label>
                  <div
                    className={`p-3 rounded-lg border ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-gray-100 border-gray-300 text-gray-800"
                    }`}
                  >
                    {SwissTaxEngine.cantons[selectedCanton]?.name ||
                      selectedCanton}
                  </div>
                </div>

                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Target Canton
                  </label>
                  <select
                    value={relocationTargetCanton}
                    onChange={(e) => setRelocationTargetCanton(e.target.value)}
                    className={`w-full p-3 rounded-lg border ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-white border-gray-300 text-gray-800"
                    } focus:ring-2 focus:ring-blue-500`}
                  >
                    {Object.entries(SwissTaxEngine.cantons).map(
                      ([code, canton]) => (
                        <option key={code} value={code}>
                          {canton.name}
                        </option>
                      )
                    )}
                  </select>
                </div>

                <div>
                  <label
                    className={`block text-sm font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Monthly Rent (Target)
                  </label>
                  <input
                    type="number"
                    value={relocationProfile.monthlyRent}
                    onChange={(e) =>
                      setRelocationProfile({
                        ...relocationProfile,
                        monthlyRent: parseFloat(e.target.value) || 0,
                      })
                    }
                    className={`w-full p-3 rounded-lg border ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-white border-gray-300 text-gray-800"
                    } focus:ring-2 focus:ring-blue-500`}
                    placeholder="2000"
                  />
                </div>
              </div>

              <div className="grid md:grid-cols-4 gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={relocationProfile.hasJobOffer}
                    onChange={(e) =>
                      setRelocationProfile({
                        ...relocationProfile,
                        hasJobOffer: e.target.checked,
                      })
                    }
                    className="mr-2"
                  />
                  <span
                    className={`text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Job Offer Secured
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={relocationProfile.isRemoteWorker}
                    onChange={(e) =>
                      setRelocationProfile({
                        ...relocationProfile,
                        isRemoteWorker: e.target.checked,
                      })
                    }
                    className="mr-2"
                  />
                  <span
                    className={`text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Remote Worker
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={relocationProfile.hasChildren}
                    onChange={(e) =>
                      setRelocationProfile({
                        ...relocationProfile,
                        hasChildren: e.target.checked,
                      })
                    }
                    className="mr-2"
                  />
                  <span
                    className={`text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Has Children
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={relocationProfile.ownsProperty}
                    onChange={(e) =>
                      setRelocationProfile({
                        ...relocationProfile,
                        ownsProperty: e.target.checked,
                      })
                    }
                    className="mr-2"
                  />
                  <span
                    className={`text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    Owns Property
                  </span>
                </label>
              </div>
            </div>

            {/* Target Canton Analysis */}
            {relocationAnalysis.targetCantonAnalysis && (
              <div
                className={`p-5 md:p-6 rounded-xl ${
                  relocationAnalysis.targetCantonAnalysis.recommendation
                    .level === "highly_recommended"
                    ? darkMode
                      ? "bg-gray-800/70 border border-green-500/25"
                      : "bg-white/90 border border-green-200 shadow-md"
                    : relocationAnalysis.targetCantonAnalysis.recommendation
                        .level === "recommended"
                    ? darkMode
                      ? "bg-gray-800/70 border border-blue-500/25"
                      : "bg-white/90 border border-blue-200 shadow-md"
                    : relocationAnalysis.targetCantonAnalysis.recommendation
                        .level === "not_recommended"
                    ? darkMode
                      ? "bg-gray-800/70 border border-red-500/25"
                      : "bg-white/90 border border-red-200 shadow-md"
                    : darkMode
                    ? "bg-gray-800/70 border border-yellow-500/25"
                    : "bg-white/90 border border-yellow-200 shadow-md"
                } backdrop-blur-sm`}
              >
                <h3
                  className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                    darkMode ? "text-white" : "text-gray-800"
                  } flex items-center`}
                >
                  <span className="mr-2 text-blue-500 text-xl">🎯</span>
                  {SwissTaxEngine.cantons[selectedCanton]?.name} →{" "}
                  {SwissTaxEngine.cantons[relocationTargetCanton]?.name}{" "}
                  Analysis
                </h3>

                <div
                  className={`mb-4 p-4 rounded-lg ${
                    relocationAnalysis.targetCantonAnalysis.recommendation
                      .level === "highly_recommended"
                      ? darkMode
                        ? "bg-green-800/30 border border-green-600/40"
                        : "bg-green-50 border border-green-200"
                      : relocationAnalysis.targetCantonAnalysis.recommendation
                          .level === "recommended"
                      ? darkMode
                        ? "bg-blue-800/30 border border-blue-600/40"
                        : "bg-blue-50 border border-blue-200"
                      : relocationAnalysis.targetCantonAnalysis.recommendation
                          .level === "not_recommended"
                      ? darkMode
                        ? "bg-red-800/30 border border-red-600/40"
                        : "bg-red-50 border border-red-200"
                      : darkMode
                      ? "bg-yellow-800/30 border border-yellow-600/40"
                      : "bg-yellow-50 border border-yellow-200"
                  }`}
                >
                  <h4
                    className={`font-semibold mb-2 ${
                      relocationAnalysis.targetCantonAnalysis.recommendation
                        .level === "highly_recommended"
                        ? darkMode
                          ? "text-green-300"
                          : "text-green-700"
                        : relocationAnalysis.targetCantonAnalysis.recommendation
                            .level === "recommended"
                        ? darkMode
                          ? "text-blue-300"
                          : "text-blue-700"
                        : relocationAnalysis.targetCantonAnalysis.recommendation
                            .level === "not_recommended"
                        ? darkMode
                          ? "text-red-300"
                          : "text-red-700"
                        : darkMode
                        ? "text-yellow-300"
                        : "text-yellow-700"
                    }`}
                  >
                    📋{" "}
                    {
                      relocationAnalysis.targetCantonAnalysis.recommendation
                        .title
                    }
                  </h4>
                  <p
                    className={`text-sm ${
                      relocationAnalysis.targetCantonAnalysis.recommendation
                        .level === "highly_recommended"
                        ? darkMode
                          ? "text-green-200"
                          : "text-green-600"
                        : relocationAnalysis.targetCantonAnalysis.recommendation
                            .level === "recommended"
                        ? darkMode
                          ? "text-blue-200"
                          : "text-blue-600"
                        : relocationAnalysis.targetCantonAnalysis.recommendation
                            .level === "not_recommended"
                        ? darkMode
                          ? "text-red-200"
                          : "text-red-600"
                        : darkMode
                        ? "text-yellow-200"
                        : "text-yellow-600"
                    }`}
                  >
                    {
                      relocationAnalysis.targetCantonAnalysis.recommendation
                        .reason
                    }
                  </p>
                  <div
                    className={`mt-2 text-xs ${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  >
                    Confidence:{" "}
                    {
                      relocationAnalysis.targetCantonAnalysis.recommendation
                        .confidence
                    }
                    %
                  </div>
                </div>

                <div className="grid md:grid-cols-4 gap-4">
                  <div
                    className={`p-4 rounded-lg text-center ${
                      darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                    }`}
                  >
                    <div
                      className={`text-2xl font-bold ${
                        relocationAnalysis.targetCantonAnalysis.financialImpact
                          .netAnnualBenefit > 0
                          ? darkMode
                            ? "text-green-400"
                            : "text-green-600"
                          : darkMode
                          ? "text-red-400"
                          : "text-red-600"
                      }`}
                    >
                      {formatCurrency(
                        relocationAnalysis.targetCantonAnalysis.financialImpact
                          .netAnnualBenefit
                      )}
                    </div>
                    <div
                      className={`text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      Net Annual Benefit
                    </div>
                  </div>

                  <div
                    className={`p-4 rounded-lg text-center ${
                      darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                    }`}
                  >
                    <div
                      className={`text-2xl font-bold ${
                        darkMode ? "text-blue-400" : "text-blue-600"
                      }`}
                    >
                      {relocationAnalysis.targetCantonAnalysis.financialImpact.breakEvenMonths.toFixed(
                        1
                      )}
                    </div>
                    <div
                      className={`text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      Break-even (months)
                    </div>
                  </div>

                  <div
                    className={`p-4 rounded-lg text-center ${
                      darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                    }`}
                  >
                    <div
                      className={`text-2xl font-bold ${
                        relocationAnalysis.targetCantonAnalysis.fireImpact
                          .yearsEarlierRetirement > 0
                          ? darkMode
                            ? "text-purple-400"
                            : "text-purple-600"
                          : darkMode
                          ? "text-gray-400"
                          : "text-gray-600"
                      }`}
                    >
                      {relocationAnalysis.targetCantonAnalysis.fireImpact.yearsEarlierRetirement.toFixed(
                        1
                      )}
                    </div>
                    <div
                      className={`text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      Years Earlier FIRE
                    </div>
                  </div>

                  <div
                    className={`p-4 rounded-lg text-center ${
                      darkMode ? "bg-gray-700/60" : "bg-gray-100/80"
                    }`}
                  >
                    <div
                      className={`text-2xl font-bold ${
                        relocationAnalysis.targetCantonAnalysis.qualityOfLife
                          .improvement
                          ? darkMode
                            ? "text-green-400"
                            : "text-green-600"
                          : darkMode
                          ? "text-red-400"
                          : "text-red-600"
                      }`}
                    >
                      {relocationAnalysis.targetCantonAnalysis.qualityOfLife
                        .qualityOfLifeDelta > 0
                        ? "+"
                        : ""}
                      {
                        relocationAnalysis.targetCantonAnalysis.qualityOfLife
                          .qualityOfLifeDelta
                      }
                    </div>
                    <div
                      className={`text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      Quality of Life
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Top Relocation Opportunities */}
            <div
              className={`p-5 md:p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800/70 border border-purple-500/25"
                  : "bg-white/90 border border-purple-200 shadow-md"
              } backdrop-blur-sm`}
            >
              <div className="flex justify-between items-center mb-4">
                <h3
                  className={`text-xl md:text-2xl font-bold ${
                    darkMode ? "text-white" : "text-gray-800"
                  } flex items-center`}
                >
                  <span className="mr-2 text-purple-500 text-xl">🏆</span> Top
                  Relocation Opportunities
                </h3>
                <button
                  onClick={() => setShowAllCantons(!showAllCantons)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium ${
                    darkMode
                      ? "bg-purple-600 hover:bg-purple-700 text-white"
                      : "bg-purple-500 hover:bg-purple-600 text-white"
                  } transition-colors`}
                >
                  {showAllCantons ? "Show Top 5" : "Show All 26 Cantons"}
                </button>
              </div>

              <div className="space-y-3">
                {relocationAnalysis.topOpportunities.map(
                  (opportunity, index) => (
                    <div
                      key={opportunity.canton}
                      className={`p-4 rounded-lg border ${
                        index === 0
                          ? darkMode
                            ? "bg-yellow-800/20 border-yellow-500"
                            : "bg-yellow-50 border-yellow-500"
                          : index < 3
                          ? darkMode
                            ? "bg-green-800/20 border-green-500"
                            : "bg-green-50 border-green-500"
                          : darkMode
                          ? "bg-gray-700/40 border-gray-600"
                          : "bg-gray-50 border-gray-200"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4
                          className={`text-lg font-semibold ${
                            darkMode ? "text-white" : "text-gray-800"
                          }`}
                        >
                          {index === 0
                            ? "🥇"
                            : index === 1
                            ? "🥈"
                            : index === 2
                            ? "🥉"
                            : `${index + 1}.`}{" "}
                          {opportunity.cantonName}
                        </h4>
                        <span
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            opportunity.recommendation.level ===
                            "highly_recommended"
                              ? "bg-green-500 text-white"
                              : opportunity.recommendation.level ===
                                "recommended"
                              ? "bg-blue-500 text-white"
                              : opportunity.recommendation.level ===
                                "not_recommended"
                              ? "bg-red-500 text-white"
                              : "bg-yellow-500 text-white"
                          }`}
                        >
                          {opportunity.recommendation.title}
                        </span>
                      </div>

                      <div className="grid md:grid-cols-5 gap-3 text-sm">
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-purple-200" : "text-purple-700"
                            }`}
                          >
                            Annual Benefit:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              opportunity.financialImpact.netAnnualBenefit > 0
                                ? darkMode
                                  ? "text-green-400"
                                  : "text-green-600"
                                : darkMode
                                ? "text-red-400"
                                : "text-red-600"
                            }`}
                          >
                            {formatCurrency(
                              opportunity.financialImpact.netAnnualBenefit
                            )}
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-purple-200" : "text-purple-700"
                            }`}
                          >
                            Lifetime Value:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            {formatCurrency(
                              opportunity.financialImpact.lifetimeNetBenefit
                            )}
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-purple-200" : "text-purple-700"
                            }`}
                          >
                            Break-even:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            {opportunity.financialImpact.breakEvenMonths.toFixed(
                              1
                            )}
                            mo
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-purple-200" : "text-purple-700"
                            }`}
                          >
                            FIRE Impact:
                          </span>
                          <span
                            className={`ml-2 font-bold ${
                              darkMode ? "text-white" : "text-gray-800"
                            }`}
                          >
                            {opportunity.fireImpact.yearsEarlierRetirement > 0
                              ? "+"
                              : ""}
                            {opportunity.fireImpact.yearsEarlierRetirement.toFixed(
                              1
                            )}
                            y
                          </span>
                        </div>
                        <div>
                          <span
                            className={`${
                              darkMode ? "text-purple-200" : "text-purple-700"
                            }`}
                          >
                            Complexity:
                          </span>
                          <span
                            className={`ml-2 font-medium ${
                              opportunity.implementationComplexity.level ===
                              "low"
                                ? "text-green-500"
                                : opportunity.implementationComplexity.level ===
                                  "medium"
                                ? "text-yellow-500"
                                : "text-red-500"
                            }`}
                          >
                            {opportunity.implementationComplexity.level.toUpperCase()}
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        )}

        {activeTab === "projections" && finalProjection && (
          <div
            className={`p-5 md:p-6 rounded-xl ${
              darkMode
                ? "bg-gray-800/70 border-blue-500/25"
                : "bg-white/90 border-blue-200 shadow-md"
            } backdrop-blur-sm`}
          >
            <h3
              className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                darkMode ? "text-white" : "text-gray-800"
              } flex items-center`}
            >
              <span className="mr-2 text-blue-400 text-xl">📈</span> Retirement
              Trajectory Table
            </h3>
            <p
              className={`mb-3 text-xs sm:text-sm ${
                darkMode ? "text-gray-400" : "text-gray-600"
              }`}
            >
              Year-by-year projection until age {retirementAge}. Tracks general
              savings and pension leaving benefits separately (current:{" "}
              {formatCurrency(parseFloat(currentPensionLeavingBenefits) || 0)}).
              Pension contribution:{" "}
              {formatCurrency(adjustedPensionContribution)}/mo (adjusted for{" "}
              {incomePercentage}% work time). Emergency fund target:{" "}
              {formatCurrency(emergencyFundTarget)} ({emergencyFundTargetMonths}{" "}
              months). Once reached, contributions redirect to investments.
              Company income starts in {companyIncomeStartYear} (age{" "}
              {companyIncomeStartAge}) and grows by {companyIncomeGrowthRate}
              %/year. Expected return: {expectedReturn}% p.a.
            </p>
            <div className="max-h-[70vh] overflow-y-auto pr-1.5 text-xs sm:text-sm">
              <table className="w-full text-left">
                <thead
                  className={`sticky top-0 ${
                    darkMode ? "bg-gray-700/90" : "bg-gray-100/90"
                  } backdrop-blur-sm z-10`}
                >
                  <tr>
                    {[
                      "Yr",
                      "Age",
                      "General",
                      "Pension",
                      "Total",
                      "Real",
                      "FIRE %",
                      "E-Fund",
                    ].map((h) => (
                      <th
                        key={h}
                        className={`py-1.5 px-1 md:px-2 font-semibold ${
                          darkMode ? "text-gray-300" : "text-gray-600"
                        }`}
                      >
                        {h}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody
                  className={
                    darkMode
                      ? "divide-y divide-gray-700"
                      : "divide-y divide-gray-200"
                  }
                >
                  {trajectory.map((e) => (
                    <tr
                      key={e.year}
                      className={
                        darkMode
                          ? "hover:bg-gray-700/50"
                          : "hover:bg-gray-50/50"
                      }
                    >
                      <td className="py-1.5 px-1 md:px-2">
                        {e.year === 0 ? "Now" : `+${e.year}y`}
                      </td>
                      <td className="py-1.5 px-1 md:px-2">{e.age}</td>
                      <td className="py-1.5 px-1 md:px-2">
                        {formatCurrency(e.balance)}
                      </td>
                      <td className="py-1.5 px-1 md:px-2">
                        {formatCurrency(e.pensionBalance)}
                      </td>
                      <td className="py-1.5 px-1 md:px-2 font-semibold">
                        {formatCurrency(e.totalBalance)}
                      </td>
                      <td className="py-1.5 px-1 md:px-2">
                        {formatCurrency(e.realBalance)}
                      </td>
                      <td className="py-1.5 px-1 md:px-2">
                        {(e.fireProgress || 0).toFixed(1)}%
                      </td>
                      <td className="py-1.5 px-1 md:px-2">
                        {e.emergencyFundComplete
                          ? "✅"
                          : `${formatCurrency(e.emergencyFundBalance || 0)}`}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === "visualizations" && (
          <div
            className={`p-5 md:p-6 rounded-xl ${
              darkMode
                ? "bg-gray-800/70 border-indigo-500/25"
                : "bg-white/90 border-indigo-200 shadow-md"
            } backdrop-blur-sm`}
          >
            <h3
              className={`text-xl md:text-2xl font-bold mb-3 md:mb-4 ${
                darkMode ? "text-white" : "text-gray-800"
              } flex items-center`}
            >
              <span className="mr-2 text-indigo-400 text-xl">📊</span>{" "}
              Interactive Budget Allocation
            </h3>
            <p
              className={`mb-3 text-xs sm:text-sm ${
                darkMode ? "text-gray-400" : "text-gray-600"
              }`}
            >
              Hover over segments for details. Center shows current savings rate
              based on fixed goals.
            </p>
            <div className="flex justify-center">
              <BudgetDonutChart
                darkMode={darkMode}
                totalExpenses={totalExpenses}
                totalSavings={totalSavingsDisplay}
                totalMonthlyIncome={totalMonthlyIncome}
                essentialExpenses={essentialExpenses}
                nonEssentialExpenses={nonEssentialExpenses}
                remaining={remaining}
                savingsRate={savingsRate}
              />
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-1.5 mt-3 text-2xs sm:text-xs">
              {[
                {
                  label: "Essential Expenses",
                  color: darkMode ? "bg-red-500" : "bg-red-600",
                },
                {
                  label: "Discretionary",
                  color: darkMode ? "bg-orange-500" : "bg-orange-600",
                },
                {
                  label: "Savings (Fixed Goals)",
                  color: darkMode ? "bg-green-500" : "bg-green-600",
                },
                {
                  label: "Available/Surplus",
                  color: darkMode ? "bg-blue-500" : "bg-blue-600",
                },
              ].map((item) => (
                <div key={item.label} className="flex items-center">
                  <div
                    className={`w-2.5 h-2.5 rounded-sm mr-1 ${item.color}`}
                  ></div>
                  <span
                    className={darkMode ? "text-gray-300" : "text-gray-700"}
                  >
                    {item.label}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === "interactiveProjection" && (
          <ProjectionChartCanvas
            trajectoryData={trajectory}
            darkMode={darkMode}
            retirementAge={retirementAge}
            formatCurrency={formatCurrency}
          />
        )}

        {activeTab === "report" && (
          <div
            className={`print-report ${darkMode ? "dark-mode" : "light-mode"}`}
          >
            <style>{`
              @media print {
                .print-report {
                  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                  font-size: 12pt;
                  line-height: 1.6;
                  color: #333;
                  background: white;
                }
                .no-print { display: none !important; }
                .page-break { page-break-before: always; }
                .report-section { break-inside: avoid; }
                h1 { color: #667eea; border-bottom: 3px solid #667eea; padding-bottom: 10px; page-break-before: always; }
                h2 { color: #764ba2; border-left: 5px solid #764ba2; padding-left: 15px; }
                .feature-box { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
                .formula { background: #f1f3f4; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; border-left: 4px solid #667eea; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #667eea; color: white; }
                .example { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 15px 0; }
                .warning { background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 15px 0; }
                .success { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 15px 0; }
              }
              .print-report { max-width: none; margin: 0; }
              .feature-box { background: ${
                darkMode ? "#374151" : "#f8f9fa"
              }; border: 1px solid ${
              darkMode ? "#4b5563" : "#e9ecef"
            }; border-radius: 8px; padding: 20px; margin: 20px 0; }
              .formula { background: ${
                darkMode ? "#1f2937" : "#f1f3f4"
              }; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; border-left: 4px solid #667eea; }
              .example { background: ${
                darkMode ? "#451a03" : "#fff3cd"
              }; border: 1px solid ${
              darkMode ? "#92400e" : "#ffeaa7"
            }; border-radius: 8px; padding: 15px; margin: 15px 0; }
              .print-header { text-align: center; padding: 2em 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin: -1em -1em 2em -1em; border-radius: 0; }
            `}</style>

            {/* Print Header */}
            <div className="print-header no-print">
              <h1
                style={{
                  color: "white",
                  border: "none",
                  fontSize: "2.5em",
                  margin: 0,
                }}
              >
                📊 Swiss Budget Pro Report
              </h1>
              <p style={{ fontSize: "1.2em", margin: "0.5em 0" }}>
                🇨🇭 Comprehensive Financial Analysis
              </p>
              <button
                onClick={() => window.print()}
                className={`mt-4 px-6 py-3 rounded-lg font-medium ${
                  darkMode
                    ? "bg-blue-600 hover:bg-blue-700"
                    : "bg-blue-500 hover:bg-blue-600"
                } text-white transition-colors`}
              >
                🖨️ Print/Save as PDF
              </button>
            </div>

            {/* Report Content */}
            <div className="report-content">
              {/* Executive Summary */}
              <div className="report-section">
                <h1>📈 Executive Summary</h1>

                <div className="feature-box">
                  <h3>Current Financial Position</h3>
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns:
                        "repeat(auto-fit, minmax(200px, 1fr))",
                      gap: "1em",
                      margin: "1em 0",
                    }}
                  >
                    <div>
                      <strong>Monthly Income:</strong>{" "}
                      {formatCurrency(totalMonthlyIncome)}
                    </div>
                    <div>
                      <strong>Monthly Expenses:</strong>{" "}
                      {formatCurrency(totalExpenses)}
                    </div>
                    <div>
                      <strong>Monthly Savings:</strong>{" "}
                      {formatCurrency(totalSavingsDisplay)}
                    </div>
                    <div>
                      <strong>Savings Rate:</strong> {savingsRate.toFixed(1)}%
                    </div>
                    <div>
                      <strong>Current Age:</strong> {currentAge}
                    </div>
                    <div>
                      <strong>Target Retirement:</strong> Age {retirementAge}
                    </div>
                    <div>
                      <strong>Years to FIRE:</strong>{" "}
                      {Math.max(0, retirementAge - currentAge)}
                    </div>
                    <div>
                      <strong>FIRE Progress:</strong>{" "}
                      {(finalProjection?.fireProgress || 0).toFixed(1)}%
                    </div>
                  </div>
                </div>

                <div className="success">
                  <strong>🎯 FIRE Target:</strong> Based on your current
                  expenses of {formatCurrency(totalExpenses)}/month, your FIRE
                  number is {formatCurrency(totalExpenses * 12 * 25)} (25x
                  annual expenses). At age {retirementAge}, your projected
                  portfolio will be{" "}
                  {formatCurrency(finalProjection?.totalBalance || 0)},
                  providing an estimated monthly income of{" "}
                  {formatCurrency(finalProjection?.monthlyIncome || 0)} using
                  the 4% rule.
                </div>
              </div>

              {/* Income Analysis */}
              <div className="report-section page-break">
                <h1>💰 Income Analysis</h1>

                <h2>Current Income Sources</h2>
                <table>
                  <thead>
                    <tr>
                      <th>Source</th>
                      <th>Monthly Amount</th>
                      <th>Annual Amount</th>
                      <th>Notes</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Primary Employment</td>
                      <td>{formatCurrency(currentPrimaryIncome)}</td>
                      <td>{formatCurrency(currentPrimaryIncome * 12)}</td>
                      <td>
                        {incomePercentage < 100
                          ? `${incomePercentage}% work time`
                          : "Full time"}
                      </td>
                    </tr>
                    <tr>
                      <td>Company Income</td>
                      <td>
                        {currentAge >= companyIncomeStartAge
                          ? formatCurrency(parseFloat(companyIncome) || 0)
                          : formatCurrency(0)}
                      </td>
                      <td>
                        {currentAge >= companyIncomeStartAge
                          ? formatCurrency(
                              (parseFloat(companyIncome) || 0) * 12
                            )
                          : formatCurrency(0)}
                      </td>
                      <td>
                        {currentAge >= companyIncomeStartAge
                          ? "Active"
                          : `Starts ${companyIncomeStartYear} (age ${companyIncomeStartAge})`}
                      </td>
                    </tr>
                    <tr>
                      <td>HSLU</td>
                      <td>{formatCurrency(hsluIncomeAmount)}</td>
                      <td>{formatCurrency(parseFloat(hsluIncome) || 0)}</td>
                      <td>Annual contract</td>
                    </tr>
                    <tr>
                      <td>RUAG</td>
                      <td>{formatCurrency(ruagIncomeAmount)}</td>
                      <td>{formatCurrency(parseFloat(ruagIncome) || 0)}</td>
                      <td>Annual contract</td>
                    </tr>
                    <tr
                      style={{
                        fontWeight: "bold",
                        backgroundColor: darkMode ? "#374151" : "#f0f0f0",
                      }}
                    >
                      <td>Total</td>
                      <td>{formatCurrency(totalMonthlyIncome)}</td>
                      <td>{formatCurrency(totalMonthlyIncome * 12)}</td>
                      <td>Current total</td>
                    </tr>
                  </tbody>
                </table>

                {incomePercentage < 100 && (
                  <div className="warning">
                    <strong>⚠️ Part-time Impact:</strong> Working at{" "}
                    {incomePercentage}% results in an annual income reduction of{" "}
                    {formatCurrency(
                      (parseFloat(monthlyIncome) || 0) *
                        (1 - incomePercentage / 100) *
                        12
                    )}
                    compared to full-time employment.
                  </div>
                )}

                <h2>Company Income Progression</h2>
                {companyIncomeProgression.length > 0 && (
                  <div>
                    <table>
                      <thead>
                        <tr>
                          <th>Year</th>
                          <th>Age</th>
                          <th>Monthly Income</th>
                          <th>Annual Income</th>
                          <th>Cumulative</th>
                        </tr>
                      </thead>
                      <tbody>
                        {companyIncomeProgression.slice(0, 10).map((entry) => (
                          <tr key={entry.year}>
                            <td>{entry.year}</td>
                            <td>{entry.age}</td>
                            <td>
                              {entry.isActive
                                ? formatCurrency(entry.monthlyIncome)
                                : "-"}
                            </td>
                            <td>
                              {entry.isActive
                                ? formatCurrency(entry.yearlyIncome)
                                : "-"}
                            </td>
                            <td>{formatCurrency(entry.cumulativeEarnings)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    <div className="example">
                      <strong>Company Income Total:</strong> From age{" "}
                      {companyIncomeStartAge} to {retirementAge}, company income
                      will contribute approximately{" "}
                      {formatCurrency(totalCompanyEarnings)}
                      to your total wealth accumulation.
                    </div>
                  </div>
                )}
              </div>

              {/* Budget Analysis */}
              <div className="report-section page-break">
                <h1>🏠 Budget Analysis</h1>

                <h2>Monthly Budget Breakdown</h2>
                <table>
                  <thead>
                    <tr>
                      <th>Category</th>
                      <th>Amount</th>
                      <th>% of Income</th>
                      <th>Annual Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Essential Expenses</td>
                      <td>{formatCurrency(essentialExpenses)}</td>
                      <td>
                        {totalMonthlyIncome > 0
                          ? (
                              (essentialExpenses / totalMonthlyIncome) *
                              100
                            ).toFixed(1)
                          : 0}
                        %
                      </td>
                      <td>{formatCurrency(essentialExpenses * 12)}</td>
                    </tr>
                    <tr>
                      <td>Discretionary Expenses</td>
                      <td>{formatCurrency(nonEssentialExpenses)}</td>
                      <td>
                        {totalMonthlyIncome > 0
                          ? (
                              (nonEssentialExpenses / totalMonthlyIncome) *
                              100
                            ).toFixed(1)
                          : 0}
                        %
                      </td>
                      <td>{formatCurrency(nonEssentialExpenses * 12)}</td>
                    </tr>
                    <tr>
                      <td>Total Savings</td>
                      <td>{formatCurrency(totalSavingsDisplay)}</td>
                      <td>{savingsRate.toFixed(1)}%</td>
                      <td>{formatCurrency(totalSavingsDisplay * 12)}</td>
                    </tr>
                    <tr>
                      <td>Remaining/Surplus</td>
                      <td>{formatCurrency(remaining)}</td>
                      <td>
                        {totalMonthlyIncome > 0
                          ? ((remaining / totalMonthlyIncome) * 100).toFixed(1)
                          : 0}
                        %
                      </td>
                      <td>{formatCurrency(remaining * 12)}</td>
                    </tr>
                  </tbody>
                </table>

                <h2>Detailed Expenses</h2>
                <table>
                  <thead>
                    <tr>
                      <th>Category</th>
                      <th>Monthly Amount</th>
                      <th>Type</th>
                      <th>Annual Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {expenses.map((exp) => (
                      <tr key={exp.id}>
                        <td>{exp.category || "Unnamed Category"}</td>
                        <td>{formatCurrency(parseFloat(exp.amount) || 0)}</td>
                        <td>{exp.essential ? "Essential" : "Discretionary"}</td>
                        <td>
                          {formatCurrency((parseFloat(exp.amount) || 0) * 12)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Savings Strategy */}
              <div className="report-section page-break">
                <h1>💎 Savings Strategy</h1>

                <h2>Savings Goals</h2>
                <table>
                  <thead>
                    <tr>
                      <th>Goal</th>
                      <th>Monthly Contribution</th>
                      <th>Annual Total</th>
                      <th>Special Features</th>
                    </tr>
                  </thead>
                  <tbody>
                    {savings.map((sav) => (
                      <tr key={sav.id}>
                        <td>{sav.goal || "Unnamed Goal"}</td>
                        <td>{formatCurrency(parseFloat(sav.amount) || 0)}</td>
                        <td>
                          {formatCurrency((parseFloat(sav.amount) || 0) * 12)}
                        </td>
                        <td>
                          {sav.goal.toLowerCase().includes("pillar 3a") &&
                            "🇨🇭 Swiss tax benefits"}
                          {sav.goal.toLowerCase().includes("emergency") &&
                            emergencyFundReached &&
                            "🎯 Target reached - redirecting"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                <h2>Emergency Fund Analysis</h2>
                <div className="feature-box">
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns:
                        "repeat(auto-fit, minmax(200px, 1fr))",
                      gap: "1em",
                    }}
                  >
                    <div>
                      <strong>Target Months:</strong>{" "}
                      {emergencyFundTargetMonths}
                    </div>
                    <div>
                      <strong>Monthly Expenses:</strong>{" "}
                      {formatCurrency(totalExpenses)}
                    </div>
                    <div>
                      <strong>Target Amount:</strong>{" "}
                      {formatCurrency(emergencyFundTarget)}
                    </div>
                    <div>
                      <strong>Current Status:</strong>{" "}
                      {emergencyFundReached
                        ? "✅ Target Reached"
                        : "⏳ Building"}
                    </div>
                  </div>

                  {emergencyFundReached && (
                    <div className="success" style={{ marginTop: "1em" }}>
                      <strong>Smart Redirection Active:</strong> Emergency fund
                      contributions ({formatCurrency(emergencyFundMonthly)}
                      /month) are now automatically redirected to your
                      investment portfolio, increasing total investment
                      contributions and accelerating your FIRE timeline.
                    </div>
                  )}
                </div>

                <h2>Swiss Pension Integration</h2>
                <table>
                  <thead>
                    <tr>
                      <th>Component</th>
                      <th>Current Balance</th>
                      <th>Monthly Contribution</th>
                      <th>Notes</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Pension Leaving Benefits (BVG)</td>
                      <td>
                        {formatCurrency(
                          parseFloat(currentPensionLeavingBenefits) || 0
                        )}
                      </td>
                      <td>{formatCurrency(adjustedPensionContribution)}</td>
                      <td>
                        {incomePercentage < 100
                          ? `Adjusted for ${incomePercentage}% work time`
                          : "Full contribution"}
                      </td>
                    </tr>
                    <tr>
                      <td>Pillar 3a</td>
                      <td>Tracked in savings goals</td>
                      <td>
                        {formatCurrency(
                          parseFloat(
                            savings.find((s) =>
                              s.goal.toLowerCase().includes("pillar 3a")
                            )?.amount || 0
                          )
                        )}
                      </td>
                      <td>🇨🇭 Annual limit: {formatCurrency(pillar3aLimit)}</td>
                    </tr>
                  </tbody>
                </table>
              </div>

              {/* FIRE Projections */}
              <div className="report-section page-break">
                <h1>🚀 FIRE Projections</h1>

                <h2>Key Assumptions</h2>
                <div className="feature-box">
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns:
                        "repeat(auto-fit, minmax(200px, 1fr))",
                      gap: "1em",
                    }}
                  >
                    <div>
                      <strong>Expected Return:</strong> {expectedReturn}%
                      annually
                    </div>
                    <div>
                      <strong>Inflation Rate:</strong> {inflationRate}% annually
                    </div>
                    <div>
                      <strong>Withdrawal Rate:</strong> 4% annually
                    </div>
                    <div>
                      <strong>Current Age:</strong> {currentAge}
                    </div>
                    <div>
                      <strong>Retirement Age:</strong> {retirementAge}
                    </div>
                    <div>
                      <strong>Years to FIRE:</strong>{" "}
                      {Math.max(0, retirementAge - currentAge)}
                    </div>
                  </div>
                </div>

                <h2>FIRE Calculations</h2>
                <div className="formula">
                  FIRE Number = Annual Expenses × 25 ={" "}
                  {formatCurrency(totalExpenses * 12)} × 25 ={" "}
                  {formatCurrency(totalExpenses * 12 * 25)}
                </div>
                <div className="formula">
                  Monthly Retirement Income = Portfolio Balance × 4% ÷ 12 ={" "}
                  {formatCurrency(finalProjection?.monthlyIncome || 0)}
                </div>

                <h2>Trajectory Summary</h2>
                <table>
                  <thead>
                    <tr>
                      <th>Metric</th>
                      <th>Current</th>
                      <th>At Retirement (Age {retirementAge})</th>
                      <th>Growth</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>General Savings</td>
                      <td>{formatCurrency(parseFloat(currentSavings) || 0)}</td>
                      <td>{formatCurrency(finalProjection?.balance || 0)}</td>
                      <td>
                        {(
                          ((finalProjection?.balance || 0) /
                            (parseFloat(currentSavings) || 1) -
                            1) *
                          100
                        ).toFixed(1)}
                        %
                      </td>
                    </tr>
                    <tr>
                      <td>Pension Balance</td>
                      <td>
                        {formatCurrency(
                          parseFloat(currentPensionLeavingBenefits) || 0
                        )}
                      </td>
                      <td>
                        {formatCurrency(finalProjection?.pensionBalance || 0)}
                      </td>
                      <td>
                        {(
                          ((finalProjection?.pensionBalance || 0) /
                            (parseFloat(currentPensionLeavingBenefits) || 1) -
                            1) *
                          100
                        ).toFixed(1)}
                        %
                      </td>
                    </tr>
                    <tr style={{ fontWeight: "bold" }}>
                      <td>Total Portfolio</td>
                      <td>
                        {formatCurrency(
                          (parseFloat(currentSavings) || 0) +
                            (parseFloat(currentPensionLeavingBenefits) || 0)
                        )}
                      </td>
                      <td>
                        {formatCurrency(finalProjection?.totalBalance || 0)}
                      </td>
                      <td>
                        {(finalProjection?.fireProgress || 0).toFixed(1)}% to
                        FIRE
                      </td>
                    </tr>
                  </tbody>
                </table>

                <div className="example">
                  <strong>Retirement Income Projection:</strong> At age{" "}
                  {retirementAge}, your portfolio of
                  {formatCurrency(finalProjection?.totalBalance || 0)} will
                  provide an estimated monthly income of
                  {formatCurrency(finalProjection?.monthlyIncome || 0)} in
                  today's purchasing power, covering{" "}
                  {(
                    ((finalProjection?.monthlyIncome || 0) / totalExpenses) *
                    100
                  ).toFixed(1)}
                  % of your current monthly expenses.
                </div>
              </div>

              {/* Mathematical Formulas */}
              <div className="report-section page-break">
                <h1>🧮 Mathematical Formulas</h1>

                <h2>Core Calculations</h2>

                <h3>1. Future Value with Regular Contributions</h3>
                <div className="formula">
                  FV = PV × (1 + r)^n + PMT × [((1 + r)^n - 1) / r]
                </div>
                <p>
                  Where: FV = Future Value, PV = Present Value, r = Return Rate,
                  n = Periods, PMT = Payment
                </p>

                <h3>2. FIRE Number</h3>
                <div className="formula">
                  FIRE Number = Annual Expenses × 25 ={" "}
                  {formatCurrency(totalExpenses * 12)} × 25 ={" "}
                  {formatCurrency(totalExpenses * 12 * 25)}
                </div>

                <h3>3. Inflation Adjustment</h3>
                <div className="formula">
                  Real Value = Nominal Value / (1 + {inflationRate / 100})^years
                </div>

                <h3>4. Savings Rate</h3>
                <div className="formula">
                  Savings Rate = ({formatCurrency(totalSavingsDisplay)} /{" "}
                  {formatCurrency(totalMonthlyIncome)}) × 100 ={" "}
                  {savingsRate.toFixed(1)}%
                </div>

                <h3>5. Work Time Adjustment</h3>
                <div className="formula">
                  Effective Income ={" "}
                  {formatCurrency(parseFloat(monthlyIncome) || 0)} × (
                  {incomePercentage}% / 100) ={" "}
                  {formatCurrency(currentPrimaryIncome)}
                </div>
                <div className="formula">
                  Adjusted Pension = {formatCurrency(basePensionContribution)} ×
                  ({incomePercentage}% / 100) ={" "}
                  {formatCurrency(adjustedPensionContribution)}
                </div>
              </div>

              {/* Recommendations */}
              <div className="report-section page-break">
                <h1>💡 Personalized Recommendations</h1>

                <h2>Current Analysis</h2>
                {targetAnalysis.recommendations.map((rec, idx) => (
                  <div
                    key={idx}
                    className={
                      rec.type === "success"
                        ? "success"
                        : rec.type === "warning"
                        ? "warning"
                        : "example"
                    }
                  >
                    <strong>
                      {rec.icon} {rec.title}:
                    </strong>{" "}
                    {rec.description}
                    <br />
                    <strong>Action:</strong> {rec.action}
                  </div>
                ))}

                <h2>Optimization Strategies</h2>
                <div className="feature-box">
                  <h3>🎯 Key Recommendations</h3>
                  <ol>
                    <li>
                      <strong>Maximize Swiss Tax Benefits:</strong>
                      {parseFloat(
                        savings.find((s) =>
                          s.goal.toLowerCase().includes("pillar 3a")
                        )?.amount || 0
                      ) *
                        12 <
                      pillar3aLimit
                        ? ` Increase Pillar 3a to ${formatCurrency(
                            pillar3aLimit / 12
                          )}/month for maximum tax deduction.`
                        : " You are maximizing Pillar 3a contributions - excellent!"}
                    </li>
                    <li>
                      <strong>Savings Rate Optimization:</strong>
                      {savingsRate < 50
                        ? ` Your current ${savingsRate.toFixed(
                            1
                          )}% savings rate is good, but consider increasing to 50%+ for accelerated FIRE.`
                        : ` Your ${savingsRate.toFixed(
                            1
                          )}% savings rate is excellent for FIRE pursuit!`}
                    </li>
                    <li>
                      <strong>Emergency Fund:</strong>
                      {emergencyFundReached
                        ? " Emergency fund target reached - excellent safety net in place!"
                        : ` Continue building emergency fund to ${formatCurrency(
                            emergencyFundTarget
                          )} target.`}
                    </li>
                    <li>
                      <strong>Work-Life Balance:</strong>
                      {incomePercentage < 100
                        ? ` Current ${incomePercentage}% work time impacts income by ${formatCurrency(
                            (parseFloat(monthlyIncome) || 0) *
                              (1 - incomePercentage / 100) *
                              12
                          )} annually. Consider if this trade-off aligns with your FIRE timeline.`
                        : " Full-time work maximizes income for FIRE acceleration."}
                    </li>
                  </ol>
                </div>

                <h2>Next Steps</h2>
                <div className="success">
                  <strong>Implementation Checklist:</strong>
                  <ol>
                    <li>Review and update actual expenses monthly</li>
                    <li>Set up automatic transfers to savings accounts</li>
                    <li>Monitor investment performance quarterly</li>
                    <li>
                      Reassess projections annually or after major life changes
                    </li>
                    <li>
                      Consider consulting with a Swiss financial advisor for tax
                      optimization
                    </li>
                  </ol>
                </div>
              </div>

              {/* Footer */}
              <div
                style={{
                  textAlign: "center",
                  marginTop: "3em",
                  padding: "2em",
                  background: darkMode ? "#374151" : "#f8f9fa",
                  borderRadius: "8px",
                }}
              >
                <h3>📊 Swiss Budget Pro</h3>
                <p>Empowering Swiss FIRE Dreams, One Projection at a Time</p>
                <p style={{ fontSize: "0.9em", opacity: 0.7 }}>
                  Report generated on {new Date().toLocaleDateString("en-CH")} |
                  Data as of current application state
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Wrap the main component in error boundary
const SwissBudgetProWithErrorBoundary = () => (
  <ErrorBoundary>
    <SwissBudgetPro />
  </ErrorBoundary>
);

// Export individual components and functions for testing
export {
  BudgetDonutChart,
  DataService,
  ErrorBoundary,
  formatCurrency,
  HistoricalChart,
  LanguageSwitcher,
  MonteCarloEngine,
  SwissBudgetPro,
  SwissEconomicDataService,
  SwissTaxEngine,
  useLocalStorage,
};

export default SwissBudgetProWithErrorBoundary;
